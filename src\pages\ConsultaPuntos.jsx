import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Search, X } from 'lucide-react';

const PageWrapper = styled.div`
  background-image: url('/hero-main.webp');
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #fff;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #D8DF20;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
`;

const SearchContainer = styled.div`
  width: 100%;
  max-width: 600px;
  margin: 0 auto 2rem;

  @media (max-width: 768px) {
    width: 90%;
    margin-bottom: 1.5rem;
  }
`;

const SearchForm = styled.form`
  display: flex;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;

  @media (max-width: 768px) {
    border-radius: 10px;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 1rem;
  font-size: 1rem;
  border: none;
  outline: none;
  background: transparent;
  color: #fff;

  &::placeholder {
    color: #666;
  }

  @media (max-width: 768px) {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
`;

const SearchButton = styled.button`
  background-color: #D8DF20;
  color: #000;
  border: none;
  padding: 0 1.5rem;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #e8ef30;
  }

  &:disabled {
    background-color: #333;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const ResetButton = styled.button`
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;

  &:hover {
    color: #fff;
  }

  @media (max-width: 768px) {
    padding: 0 0.75rem;
  }
`;

const ErrorMessage = styled.div`
  color: #ff4444;
  padding: 1rem;
  border-radius: 8px;
  background-color: rgba(255, 68, 68, 0.1);
  margin-bottom: 2rem;
  text-align: center;
  width: 100%;
  max-width: 600px;
  border: 1px solid rgba(255, 68, 68, 0.2);

  @media (max-width: 768px) {
    width: 90%;
    padding: 0.75rem;
    font-size: 0.9rem;
  }
`;

const InstructionText = styled.p`
  color: #ffffff;
  text-align: center;
  margin-bottom: 2rem;
  max-width: 600px;
  line-height: 1.6;
  font-size: 1.1rem;

  @media (max-width: 768px) {
    width: 90%;
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
`;

const ConsultaPuntos = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setError('Por favor ingresa un correo electrónico');
      return;
    }
    
    setLoading(true);
    navigate(`/consultar-puntos/${encodeURIComponent(email.trim().toLowerCase())}`);
  };
  
  const resetForm = () => {
    setEmail('');
    setError(null);
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <Title>Consulta tus Puntos</Title>
        
        <InstructionText>
          Ingresa tu correo electrónico para consultar los puntos acumulados en EME Fest. 
          Los puntos pueden ser canjeados por beneficios exclusivos durante el evento.
        </InstructionText>
        
        <SearchContainer>
          <SearchForm onSubmit={handleSubmit}>
            <SearchInput 
              type="email" 
              placeholder="Ingresa tu correo electrónico" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            {email && (
              <ResetButton type="button" onClick={resetForm}>
                <X size={20} />
              </ResetButton>
            )}
            <SearchButton type="submit" disabled={loading}>
              {loading ? 'Buscando...' : <Search size={20} />}
            </SearchButton>
          </SearchForm>
        </SearchContainer>
        
        {error && <ErrorMessage>{error}</ErrorMessage>}
      </PageWrapper>
    </>
  );
};

export default ConsultaPuntos; 