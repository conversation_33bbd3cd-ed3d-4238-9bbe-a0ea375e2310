import { create } from 'zustand'
import { supabase } from '../utils/supabaseClient'

export const useRegisterStore = create((set) => ({
  isRegistering: false,

  register: async ({ email, password, name, lastname }) => {
    set({ isRegistering: true })

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          lastname
        }
      }
    })

    set({ isRegistering: false })
    if (error) throw error

    return data
  }
}))
