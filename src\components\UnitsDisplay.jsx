import React from 'react';
import styled from 'styled-components';
import { Building2 } from 'lucide-react';

const UnitsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const UnitBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: ${props => props.$variant === 'primary' ? '#F16925' : '#e9ecef'};
  color: ${props => props.$variant === 'primary' ? 'white' : '#495057'};
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
`;

const UnitsText = styled.span`
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
`;

const UnitsLabel = styled.span`
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-right: 0.25rem;
`;

const IconWrapper = styled.span`
  display: inline-flex;
  align-items: center;
  color: ${props => props.$variant === 'primary' ? 'white' : '#6c757d'};
`;

/**
 * UnitsDisplay Component
 * 
 * Displays unit numbers in a consistent, visually appealing format
 * Handles both single and multiple units gracefully
 * 
 * @param {Object} props
 * @param {Array|string} props.units - Array of unit numbers or single unit string
 * @param {string} props.fallbackUnit - Fallback single unit value (legacy support)
 * @param {string} props.variant - Display variant: 'badges', 'text', 'compact'
 * @param {boolean} props.showIcon - Whether to show building icon
 * @param {boolean} props.showLabel - Whether to show "Units:" label
 * @param {string} props.language - Language for labels ('en' or 'es')
 */
const UnitsDisplay = ({
  units,
  fallbackUnit,
  variant = 'badges',
  showIcon = false,
  showLabel = false,
  language = 'en'
}) => {
  // Determine the units to display
  let displayUnits = [];
  
  if (Array.isArray(units) && units.length > 0) {
    // Use the units array if it exists and has content
    displayUnits = units.filter(unit => unit && unit.toString().trim() !== '');
  } else if (fallbackUnit && fallbackUnit.toString().trim() !== '') {
    // Fall back to the single unit field
    // Handle both comma-separated and space-separated values in the fallback unit
    const unitStr = fallbackUnit.toString().trim();

    // Try comma separation first, then space separation
    let parsedUnits;
    if (unitStr.includes(',')) {
      parsedUnits = unitStr.split(',').map(u => u.trim()).filter(u => u !== '');
    } else if (unitStr.includes(' ') && !unitStr.includes('Payment Agreement') && !unitStr.includes('Cooking Oil')) {
      // Handle space-separated units, but exclude descriptive text like "Payment Agreement 1"
      parsedUnits = unitStr.split(/\s+/).map(u => u.trim()).filter(u => u !== '');
    } else {
      // Single unit
      parsedUnits = [unitStr];
    }

    displayUnits = parsedUnits;
  }

  // If no units to display, return null or a placeholder
  if (displayUnits.length === 0) {
    return variant === 'text' ? (
      <UnitsText>{language === 'es' ? 'N/D' : 'N/A'}</UnitsText>
    ) : null;
  }

  // Get labels based on language
  const labels = {
    unit: language === 'es' ? 'Unidad' : 'Unit',
    units: language === 'es' ? 'Unidades' : 'Units'
  };

  const isMultiple = displayUnits.length > 1;
  const label = isMultiple ? labels.units : labels.unit;

  // Render based on variant
  switch (variant) {
    case 'badges':
      return (
        <UnitsContainer>
          {showLabel && <UnitsLabel>{label}:</UnitsLabel>}
          {displayUnits.map((unit, index) => (
            <UnitBadge key={index} $variant={index === 0 ? 'primary' : 'secondary'}>
              {showIcon && index === 0 && (
                <IconWrapper $variant={index === 0 ? 'primary' : 'secondary'}>
                  <Building2 size={14} />
                </IconWrapper>
              )}
              {unit}
            </UnitBadge>
          ))}
        </UnitsContainer>
      );

    case 'text':
      return (
        <UnitsContainer>
          {showIcon && (
            <IconWrapper>
              <Building2 size={16} />
            </IconWrapper>
          )}
          {showLabel && <UnitsLabel>{label}:</UnitsLabel>}
          <UnitsText>{displayUnits.join(', ')}</UnitsText>
        </UnitsContainer>
      );

    case 'compact':
      return (
        <UnitsContainer>
          {showIcon && (
            <IconWrapper>
              <Building2 size={14} />
            </IconWrapper>
          )}
          <UnitsText>
            {isMultiple ? `${label} ` : ''}
            {displayUnits.join(', ')}
          </UnitsText>
        </UnitsContainer>
      );

    default:
      return (
        <UnitsContainer>
          <UnitsText>{displayUnits.join(', ')}</UnitsText>
        </UnitsContainer>
      );
  }
};

export default UnitsDisplay;
