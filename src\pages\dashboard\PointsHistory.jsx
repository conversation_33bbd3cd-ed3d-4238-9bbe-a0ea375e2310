import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUserStore } from '../../store/useUserStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';
import { Download, Search } from 'lucide-react';
import * as XLSX from 'xlsx';

const Container = styled(motion.div)`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const SearchContainer = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  max-width: 400px;

  @media (max-width: 768px) {
    max-width: 100%;
  }
`;

const SearchInput = styled.input`
  border: none;
  outline: none;
  flex: 1;
  font-size: 1rem;
  color: #2c3e50;

  &::placeholder {
    color: #adb5bd;
  }
`;

const ExportButton = styled.button`
  background: #D8DF20;
  color: black;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;

  &:hover {
    background: #C4CA1D;
    transform: translateY(-2px);
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Th = styled.th`
  padding: 1rem;
  text-align: left;
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
`;

const Td = styled.td`
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  color: #2c3e50;
`;

const Tr = styled.tr`
  &:hover {
    background: #f8f9fa;
  }
`;

const Loading = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`;

const PointsHistory = () => {
  const { standId } = useUserStore();
  const { language } = useLanguageStore();
  const [pointsHistory, setPointsHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchPointsHistory();
  }, [standId]);

  const fetchPointsHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('points_history')
        .select('*')
        .eq('stand_id', standId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPointsHistory(data || []);
    } catch (error) {
      console.error('Error fetching points history:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredHistory = pointsHistory.filter(item => 
    item.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.user_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const exportToExcel = () => {
    const data = filteredHistory.map(item => ({
      [language === 'es' ? 'Fecha' : 'Date']: new Date(item.created_at).toLocaleDateString(),
      'Email': item.user_email,
      [language === 'es' ? 'Nombre' : 'Name']: item.user_name,
      [language === 'es' ? 'Puntos' : 'Points']: item.points
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, language === 'es' ? 'Historial de Puntos' : 'Points History');
    XLSX.writeFile(wb, language === 'es' ? 'historial_puntos.xlsx' : 'points_history.xlsx');
  };

  if (loading) {
    return <Loading>{language === 'es' ? 'Cargando historial...' : 'Loading history...'}</Loading>;
  }

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Title>{language === 'es' ? 'Historial de Puntos Asignados' : 'Assigned Points History'}</Title>

      <Header>
        <SearchContainer>
          <Search size={20} color="#666" />
          <SearchInput
            type="text"
            placeholder={language === 'es' ? 'Buscar por email o nombre...' : 'Search by email or name...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>
        <ExportButton onClick={exportToExcel}>
          <Download size={16} />
          {language === 'es' ? 'Exportar a Excel' : 'Export to Excel'}
        </ExportButton>
      </Header>

      <Table>
        <thead>
          <tr>
            <Th>{language === 'es' ? 'Fecha' : 'Date'}</Th>
            <Th>Email</Th>
            <Th>{language === 'es' ? 'Nombre' : 'Name'}</Th>
            <Th>{language === 'es' ? 'Puntos' : 'Points'}</Th>
          </tr>
        </thead>
        <tbody>
          {filteredHistory.map((item) => (
            <Tr key={item.id}>
              <Td>{new Date(item.created_at).toLocaleDateString()}</Td>
              <Td>{item.user_email}</Td>
              <Td>{item.user_name}</Td>
              <Td>{item.points}</Td>
            </Tr>
          ))}
        </tbody>
      </Table>
    </Container>
  );
};

export default PointsHistory; 