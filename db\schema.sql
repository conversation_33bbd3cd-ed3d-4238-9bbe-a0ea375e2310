-- Tabla de usuarios
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    name TEXT NOT NULL,
    lastname TEXT NOT NULL,
    points INTEGER DEFAULT 0,
    role TEXT NOT NULL DEFAULT 'general',
    email TEXT UNIQUE NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    rol TEXT
);

-- <PERSON>bla de stands
CREATE TABLE stands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    banner_url TEXT,
    whatsapp TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    avatar TEXT,
    description TEXT,
    category TEXT,
    facebook TEXT,
    instagram TEXT,
    linkedin TEXT,
    "tarjeta-digital" TEXT,
    ai_stands TEXT,
    user_id UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de puntos por stand
CREATE TABLE stand_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stand_id UUID REFERENCES stands(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    points INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de análisis de stands
CREATE TABLE stand_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stand_id UUID REFERENCES stands(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de posts/productos
CREATE TABLE posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stand_id UUID REFERENCES stands(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    precio TEXT,
    category TEXT
);

-- Tabla de historial de puntos
CREATE TABLE points_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stand_id UUID REFERENCES stands(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    points INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_email TEXT NOT NULL,
    user_name TEXT NOT NULL,
    stand_name TEXT NOT NULL
);

-- Función para manejar la inserción automática de usuarios
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, created_at)
    VALUES (new.id, new.email, now());
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para ejecutar la función cuando se crea un nuevo usuario en auth.users
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Comandos para eliminar el trigger y la función
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();
