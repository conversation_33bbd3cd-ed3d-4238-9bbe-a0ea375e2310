# User Creation Script Examples

## How to Use the Script

1. Open the Supabase SQL Editor
2. Copy the script from `create_local_user_script.sql`
3. Modify the variables in the `DECLARE` section with your user's information
4. Run the script
5. The user will be immediately ready for login

## Example 1: Pizza Restaurant

```sql
-- Modify these variables in the script:
user_email TEXT := '<EMAIL>';
user_password TEXT := 'Valores7%';
user_first_name TEXT := '<PERSON>';
user_last_name TEXT := 'Rossi';

stand_name TEXT := 'Pizza Roma';
stand_slug TEXT := 'pizza-roma';
stand_description TEXT := 'Authentic Italian pizza made with fresh ingredients';
stand_category TEXT := 'Italian Restaurant';
```

## Example 2: Coffee Shop

```sql
-- Modify these variables in the script:
user_email TEXT := '<EMAIL>';
user_password TEXT := 'Valores7%';
user_first_name TEXT := '<PERSON>';
user_last_name TEXT := '<PERSON>';

stand_name TEXT := 'Brew Cafe';
stand_slug TEXT := 'brew-cafe';
stand_description TEXT := 'Premium coffee and artisanal pastries';
stand_category TEXT := 'Coffee Shop';
```

## Example 3: Food Truck

```sql
-- Modify these variables in the script:
user_email TEXT := '<EMAIL>';
user_password TEXT := 'Valores7%';
user_first_name TEXT := 'Carlos';
user_last_name TEXT := 'Martinez';

stand_name TEXT := 'Taco Express';
stand_slug TEXT := 'taco-express';
stand_description TEXT := 'Authentic Mexican street tacos on wheels';
stand_category TEXT := 'Food Truck';
```

## Categories You Can Use

- Restaurant
- Italian Restaurant
- Mexican Restaurant
- Asian Restaurant
- Japanese Restaurant
- Vietnamese Restaurant
- Peruvian Restaurant
- Middle Eastern Restaurant
- BBQ Restaurant
- Fine Dining
- Coffee Shop
- Food Truck
- Bakery
- Snack Bar
- Fast Food
- Deli
- Cafe

## Important Notes

- **Email**: Must be unique and valid format
- **Password**: Use `Valores7%` to maintain consistency, or create a strong password
- **Slug**: Should be lowercase, URL-friendly (use hyphens instead of spaces)
- **Both `role` and `rol`**: Automatically set to "local" - this is crucial for dashboard access
- **Email Verification**: Automatically set to true for immediate login capability

## Verification

After running the script, you can verify the user was created correctly by running:

```sql
-- Check user profile
SELECT 
    email,
    name || ' ' || lastname as full_name,
    role,
    rol,
    points
FROM public.users 
WHERE email = '<EMAIL>';

-- Check associated stand
SELECT 
    name,
    slug,
    category,
    description
FROM public.stands 
WHERE email = '<EMAIL>';
```

## Troubleshooting

- **"User already exists"**: Check if the email is already in use
- **Login fails**: Verify both `role` and `rol` are set to "local"
- **Stand not showing**: Ensure the stand was created and linked to the user_id 