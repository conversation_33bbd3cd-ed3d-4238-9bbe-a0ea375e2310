import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Send, Bot, X, Loader2 } from 'lucide-react';
import useAnalytics from '../hooks/useAnalytics';

const ChatbotContainer = styled.div`
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
`;

const ChatButton = styled.button`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #D8DF20;
  border: none;
  color: black;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
`;

const ChatWindow = styled.div`
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ChatHeader = styled.div`
  padding: 1rem;
  background: #D8DF20;
  color: black;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ChatTitle = styled.h3`
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: black;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }
`;

const MessagesContainer = styled.div`
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Message = styled.div`
  max-width: 80%;
  padding: 0.8rem 1rem;
  border-radius: 12px;
  line-height: 1.4;
  position: relative;
  ${props => props.isUser ? `
    background: #D8DF20;
    color: black;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
  ` : `
    background: #f4f4f9;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
  `}
`;

const InputContainer = styled.div`
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 0.5rem;
`;

const Input = styled.input`
  flex: 1;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: #D8DF20;
  }
`;

const SendButton = styled.button`
  background: #D8DF20;
  border: none;
  color: black;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: scale(1.1);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
`;

const ChatbotWidget = ({ slug }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);
  const { recordAIInteraction } = useAnalytics();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage = input.trim();
    setInput('');
    setMessages(prev => [...prev, { text: userMessage, isUser: true }]);
    setIsLoading(true);
    setError(null);

    try {
      // Registrar la interacción con el chatbot
      await recordAIInteraction();

      console.log('Enviando mensaje:', { pregunta: userMessage, slug });
      
      const response = await fetch('https://emeia-chatbot.vercel.app/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          pregunta: userMessage,
          slug: slug
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error en la comunicación con el servidor');
      }

      const data = await response.json();
      console.log('Respuesta recibida:', data);
      
      if (data.error) {
        throw new Error(data.error);
      }

      setMessages(prev => [...prev, { text: data.respuesta, isUser: false }]);
    } catch (error) {
      console.error('Error en el chat:', error);
      setError(error.message);
      setMessages(prev => [...prev, { 
        text: `Lo siento, ha ocurrido un error: ${error.message}. Por favor, intenta de nuevo más tarde.`, 
        isUser: false 
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <ChatbotContainer>
      {isOpen && (
        <ChatWindow>
          <ChatHeader>
            <ChatTitle>
              <Bot size={20} />
              Asistente Virtual
            </ChatTitle>
            <CloseButton onClick={() => setIsOpen(false)}>
              <X size={20} />
            </CloseButton>
          </ChatHeader>
          <MessagesContainer>
            {messages.length === 0 && (
              <Message isUser={false}>
                ¡Hola! Soy el asistente virtual de este stand. ¿En qué puedo ayudarte?
              </Message>
            )}
            {messages.map((message, index) => (
              <Message key={index} isUser={message.isUser}>
                {message.text}
              </Message>
            ))}
            {isLoading && (
              <Message isUser={false}>
                <LoadingIndicator>
                  <Loader2 size={16} className="animate-spin" />
                  Pensando...
                </LoadingIndicator>
              </Message>
            )}
            {error && (
              <Message isUser={false} style={{ background: '#fee2e2', color: '#dc2626' }}>
                {error}
              </Message>
            )}
            <div ref={messagesEndRef} />
          </MessagesContainer>
          <InputContainer>
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Escribe tu mensaje..."
              disabled={isLoading}
            />
            <SendButton onClick={handleSend} disabled={isLoading || !input.trim()}>
              <Send size={20} />
            </SendButton>
          </InputContainer>
        </ChatWindow>
      )}
      <ChatButton onClick={() => setIsOpen(true)}>
        <Bot size={24} />
      </ChatButton>
    </ChatbotContainer>
  );
};

export default ChatbotWidget; 