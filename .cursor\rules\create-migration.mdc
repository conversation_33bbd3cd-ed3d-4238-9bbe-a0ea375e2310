---
description: 
globs: 
alwaysApply: true
---
# Database: Create migration

You're a Supabase Postgres expert in writing database migrations. Generate **high-quality PostgreSQL migrations** that adhere to the following best practices:

## General Guidelines

1. **Use Idempotent Operations:**
   - Include `IF NOT EXISTS` for CREATE operations
   - Include `IF EXISTS` for DROP operations
   - Use `ALTER TABLE ... ADD COLUMN IF NOT EXISTS` when available

2. **Versioned Migration Files:**
   - Use timestamp-based naming: `YYYYMMDDHHMMSS_migration_name.sql`
   - Include descriptive names that explain what the migration does

3. **Atomic Operations:**
   - Wrap related changes in transactions when necessary
   - Ensure migrations can be safely rolled back

## Best Practices

1. **Table Creation:**
   - Always include primary keys
   - Use appropriate data types
   - Add constraints and indexes
   - Include created_at/updated_at timestamps where appropriate

2. **Foreign Keys:**
   - Use proper referential integrity
   - Consider CASCADE options carefully
   - Name constraints explicitly

3. **Indexes:**
   - Add indexes for commonly queried columns
   - Use partial indexes when appropriate
   - Consider composite indexes for multi-column queries

4. **Row Level Security (RLS):**
   - Enable RLS on tables containing user data
   - Create appropriate policies for different user roles
   - Use auth.uid() for user-specific access

## Example Templates

### Create Table Migration

```sql
-- Create users profile table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    bio TEXT,
    
    CONSTRAINT username_length CHECK (char_length(username) >= 3)
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Public profiles are viewable by everyone" 
    ON public.profiles FOR SELECT 
    USING (true);

CREATE POLICY "Users can insert their own profile" 
    ON public.profiles FOR INSERT 
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
    ON public.profiles FOR UPDATE 
    USING (auth.uid() = id);

-- Create indexes
CREATE INDEX IF NOT EXISTS profiles_username_idx ON public.profiles(username);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_updated_at_profiles
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();
```

### Add Column Migration

```sql
-- Add new column to existing table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS website TEXT;

-- Add constraint if needed
ALTER TABLE public.profiles 
ADD CONSTRAINT website_url_format 
CHECK (website ~* '^https?://.*' OR website IS NULL);
```

### Create Junction Table Migration

```sql
-- Create many-to-many relationship table
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    role_id UUID REFERENCES public.roles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    UNIQUE(user_id, role_id)
);

-- Enable RLS
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own roles" 
    ON public.user_roles FOR SELECT 
    USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX IF NOT EXISTS user_roles_user_id_idx ON public.user_roles(user_id);
CREATE INDEX IF NOT EXISTS user_roles_role_id_idx ON public.user_roles(role_id);
```

### Storage Bucket Migration

```sql
-- Create storage bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies
CREATE POLICY "Avatar images are publicly accessible" 
    ON storage.objects FOR SELECT 
    USING (bucket_id = 'avatars');

CREATE POLICY "Anyone can upload an avatar" 
    ON storage.objects FOR INSERT 
    WITH CHECK (bucket_id = 'avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update their own avatar" 
    ON storage.objects FOR UPDATE 
    USING (auth.uid()::text = (storage.foldername(name))[1] AND bucket_id = 'avatars');

CREATE POLICY "Users can delete their own avatar" 
    ON storage.objects FOR DELETE 
    USING (auth.uid()::text = (storage.foldername(name))[1] AND bucket_id = 'avatars');
```

### Function Migration

```sql
-- Create or replace function
CREATE OR REPLACE FUNCTION public.get_user_profile(user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
DECLARE
    result JSON;
BEGIN
    SELECT to_json(p.*)
    INTO result
    FROM public.profiles p
    WHERE p.id = user_id;
    
    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_user_profile(UUID) TO authenticated;
```

### Rollback Guidelines

```sql
-- Always consider rollback operations
-- For the above table creation, rollback would be:

-- DROP TRIGGER IF EXISTS handle_updated_at_profiles ON public.profiles;
-- DROP FUNCTION IF EXISTS public.handle_updated_at();
-- DROP TABLE IF EXISTS public.profiles;
```

