// src/pages/Itinerary.jsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { ItineraryDay } from '../components/ItineraryDay';
import { itinerary } from '../utils/itineraryData';
import Hero from '../components/Hero'
import { Calendar } from 'lucide-react';

const PageWrapper = styled.div`
  padding: 2rem;
  background-color: #000000;
  min-height: 100vh;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #ffffff;
  font-family: 'Bebas Neue', sans-serif;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #ffffff;
  max-width: 800px;
  margin: 0 auto;
`;

const Tabs = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
  flex-wrap: wrap;
`;

const TabButton = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${({ $active }) => ($active ? '#D8DF20' : '#fff')};
  border: 2px solid ${({ $active }) => ($active ? '#D8DF20' : '#ddd')};
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s;
  color: ${({ $active }) => ($active ? '#000000' : '#333')};
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  &:hover {
    background: ${({ $active }) => ($active ? '#D8DF20' : '#f8f8f8')};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
`;

const TimelineContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

export const Itinerary = () => {
  const [day, setDay] = useState('day1');

  return (
    <>
      <Navbar />
      <Hero />
      <PageWrapper>
        <Header>
          <Title>
            <Calendar size={40} />
            Itinerario EME FEST
          </Title>
          <Subtitle>
            Plan de actividades por días. Cada día está lleno de conferencias emocionantes, 
            sesiones de networking y talleres interactivos.
          </Subtitle>
        </Header>
        <Tabs>
          {Object.keys(itinerary).map((key) => (
            <TabButton 
              key={key} 
              $active={day === key} 
              onClick={() => setDay(key)}
            >
              Día {key.replace('day', '')}
            </TabButton>
          ))}
        </Tabs>
        <TimelineContainer>
          <ItineraryDay events={itinerary[day]} />
        </TimelineContainer>
      </PageWrapper>
    </>
  );
};
