import React from 'react';
import { getOptimizedImageProps, LARGE_IMAGES } from '../utils/imageOptimization';

const OptimizedImage = ({ 
  src, 
  alt, 
  loading = 'lazy', 
  className = '',
  style = {},
  ...props 
}) => {
  // Check if this image has optimized versions
  const optimizedImage = LARGE_IMAGES[src];
  
  if (optimizedImage) {
    return (
      <picture>
        {/* WebP source for modern browsers */}
        <source 
          srcSet={optimizedImage.optimized} 
          type="image/webp" 
          sizes={optimizedImage.sizes}
        />
        
        {/* Fallback for older browsers */}
        <img 
          src={optimizedImage.fallback}
          alt={alt}
          loading={loading}
          decoding="async"
          fetchpriority={loading === 'eager' ? 'high' : 'auto'}
          className={className}
          style={style}
          sizes={optimizedImage.sizes}
          {...props}
        />
      </picture>
    );
  }
  
  // For non-optimized images, use regular img with optimized props
  const imageProps = getOptimizedImageProps(src, alt, loading);
  
  return (
    <img 
      {...imageProps}
      className={className}
      style={style}
      {...props}
    />
  );
};

export default OptimizedImage; 