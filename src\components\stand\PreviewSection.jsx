import React from 'react';
import styled from 'styled-components';
import { 
  Mail, Phone, Instagram, Facebook, Linkedin, ExternalLink 
} from 'lucide-react';
import ImageUpload from '../ImageUpload';

const PreviewSection = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  margin-bottom: 2rem;
  padding-bottom: 5vh;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  padding-bottom: 1.5rem;
`;

const PreviewBanner = styled.div`
  width: 100%;
  height: 200px;
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  margin-bottom: 4rem;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: visible;
  background-color: #f8f9fa;
`;

const AvatarContainer = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid white;
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #f8f9fa;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const PreviewContent = styled.div`
  margin-top: 2rem;
  padding: 0 30px;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
  z-index: 1;
`;

const PreviewName = styled.h2`
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const PreviewCategory = styled.span`
  display: inline-block;  background: #F16925;
  color: #fff;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.5rem;
`;

const PreviewDescription = styled.p`
  color: #6c757d;
  margin: 1.5rem 0;
  line-height: 1.6;
  font-size: 1rem;
`;

const PreviewContact = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
`;

const ContactItem = styled.a`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  text-decoration: none;
  transition: color 0.2s;
  font-size: 0.9rem;

  &:hover {
    color: #D8DF20;
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
`;

const SocialIcon = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f8f9fa;
  color: #495057;
  transition: all 0.2s;
  border: 1px solid #dee2e6;

  &:hover {
    color: #D8DF20;
    transform: translateY(-2px);
    border-color: #D8DF20;
  }
`;

const PreviewButton = styled.button`
  background: #0066cc;
  color: white;
  width: 100%;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #0052a3;
  }
`;

const StandPreview = ({ formData, onImageUpload, user }) => {
  return (
    <PreviewSection>
      <Title>Mi Stand</Title>
      <PreviewBanner>
        <ImageUpload
          currentImage={formData.banner_url}
          onImageUpload={(url) => onImageUpload('banner', url)}
          type="banner"
          userId={user?.id}
          $isAvatar={false}
        />
      </PreviewBanner>
      
      <AvatarContainer>
        <ImageUpload
          currentImage={formData.avatar}
          onImageUpload={(url) => onImageUpload('avatar', url)}
          type="avatar"
          userId={user?.id}
          $isAvatar={true}
        />
      </AvatarContainer>
      
      <PreviewContent>
        <PreviewName>{formData.name || 'Nombre del Stand'}</PreviewName>
        {formData.category && <PreviewCategory>{formData.category}</PreviewCategory>}
        <PreviewDescription>{formData.description || 'Descripción del stand...'}</PreviewDescription>
        
        <PreviewContact>
          {formData.email && (
            <ContactItem href={`mailto:${formData.email}`}>
              <Mail size={20} />
              {formData.email}
            </ContactItem>
          )}
          {formData.whatsapp && (
            <ContactItem href={`https://wa.me/${formData.whatsapp}`}>
              <Phone size={20} />
              {formData.whatsapp}
            </ContactItem>
          )}
        </PreviewContact>

        <SocialLinks>
          {formData.facebook && (
            <SocialIcon href={formData.facebook} target="_blank" rel="noopener noreferrer">
              <Facebook size={20} />
            </SocialIcon>
          )}
          {formData.instagram && (
            <SocialIcon href={formData.instagram} target="_blank" rel="noopener noreferrer">
              <Instagram size={20} />
            </SocialIcon>
          )}
          {formData.linkedin && (
            <SocialIcon href={formData.linkedin} target="_blank" rel="noopener noreferrer">
              <Linkedin size={20} />
            </SocialIcon>
          )}
        </SocialLinks>

        {formData['tarjeta-digital'] && (
          <PreviewButton
            as="a"
            href={formData['tarjeta-digital']}
            target="_blank"
            rel="noopener noreferrer"
          >
            <ExternalLink size={20} style={{ marginRight: '0.5rem' }} />
            Catalogo Digital
          </PreviewButton>
        )}
      </PreviewContent>
    </PreviewSection>
  );
};

export default StandPreview; 