// src/components/TimelineEvent.jsx
import React from 'react';
import { VerticalTimelineElement } from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import { Users, Clock, MessageCircle, Coffee, BookOpen, Calendar } from 'lucide-react';

const getEventColor = (type) => {
  switch (type) {
    case 'conference':
      return '#ff6f61';
    case 'meet':
      return '#4CAF50';
    case 'qa':
      return '#2196F3';
    case 'break':
      return '#FFC107';
    case 'workshop':
      return '#9C27B0';
    default:
      return '#607D8B';
  }
};

const getEventIcon = (type) => {
  switch (type) {
    case 'conference':
      return <BookOpen size={20} />;
    case 'meet':
      return <Users size={20} />;
    case 'qa':
      return <MessageCircle size={20} />;
    case 'break':
      return <Coffee size={20} />;
    case 'workshop':
      return <Calendar size={20} />;
    default:
      return <Clock size={20} />;
  }
};

export const TimelineEvent = ({ time, title, description, type }) => {
  const color = getEventColor(type);
  const icon = getEventIcon(type);

  return (
    <VerticalTimelineElement
      date={time}
      iconStyle={{ background: color, color: '#fff', display: 'flex', alignItems: 'flex-start', justifyContent: 'flex-start' }}
      contentStyle={{ background: '#fff', color: '#333', borderRadius: '8px', boxShadow: '0 3px 6px rgba(0,0,0,0.1)' }}
      contentArrowStyle={{ borderRight: `7px solid ${color}` }}
      icon={icon}
    >
      <h3 style={{ margin: '0 0 10px', fontSize: '1.25rem' }}>{title}</h3>
      <p style={{ margin: 0 }}>{description}</p>
    </VerticalTimelineElement>
  );
};
