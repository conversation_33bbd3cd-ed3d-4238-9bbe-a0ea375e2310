---
description: 
globs: 
alwaysApply: true
---
# FoodMaker Project Rules

## Supabase Connection
Always connect to the FoodMaker Supabase project with the following configuration:

**Required Environment Variables:**
- `VITE_SUPABASE_URL=https://lbtplhwsqqqckjjyuzas.supabase.co`
- `VITE_SUPABASE_ANON_KEY=[anon_key_for_project]`

**Connection Rules:**
1. **Always use the FoodMaker Supabase instance**: When working with database operations, authentication, or any Supabase functionality, ensure connection to `https://lbtplhwsqqqckjjyuzas.supabase.co`
2. **Environment Variable Priority**: Use `import.meta.env.VITE_SUPABASE_URL` to access the Supabase URL rather than hardcoding
3. **Error Handling**: Maintain the existing error handling in `src/utils/supabaseClient.js` that checks for missing environment variables
4. **Client Instance**: Always import and use the configured client from `src/utils/supabaseClient.js` rather than creating new client instances

**Implementation Pattern:**
```javascript
import { supabase } from '../utils/supabaseClient'
// Use the configured supabase client for all operations
```

## Project Context
- **Project Name**: FoodMaker  
- **Supabase Project**: https://lbtplhwsqqqckjjyuzas.supabase.co
- **Architecture**: Vite + React application with centralized Supabase client
- **Database Access**: All database operations should go through the single configured supabase client instance


This ensures consistency across the entire FoodMaker application and prevents accidental connections to different Supabase projects. 