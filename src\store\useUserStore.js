import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '../utils/supabaseClient'

export const useUserStore = create(persist(
  (set) => ({
    userData: null,
    role: null,
    standId: null,
    standData: null,
    loading: false,
    error: null,

    fetchUserData: async (userId) => {
      try {
        set({ loading: true, error: null });
        
        const { data, error } = await supabase
          .from('users')
          .select('*, stands(*)')
          .eq('id', userId)
          .single();

        if (error) throw error;

        const standId = data.stands?.[0]?.id || null;
        const standData = data.stands?.[0] || null;

        set({
          userData: data,
          role: data.role,
          standId: standId,
          standData: standData,
          loading: false,
          error: null
        });

        return { userData: data, standId, standData, error: null };
      } catch (error) {
        console.error('Error al obtener datos del usuario:', error);
        set({ 
          loading: false, 
          error: error.message 
        });
        return { userData: null, standId: null, standData: null, error: error.message };
      }
    },

    // Nueva función para refrescar datos del usuario
    refreshUserData: async (userId) => {
      return await useUserStore.getState().fetchUserData(userId);
    },

    clearUserData: () => {
      set({ 
        userData: null, 
        role: null, 
        standId: null,
        standData: null,
        loading: false,
        error: null
      });
    },

    setUserData: (data) => {
      const standId = data.stands?.[0]?.id || null;
      const standData = data.stands?.[0] || null;
      
      set({ 
        userData: data,
        role: data.role,
        standId: standId,
        standData: standData,
        loading: false,
        error: null
      });
    },

    updateStandData: (data) => {
      set({ 
        standData: data,
        loading: false,
        error: null
      });
    },

    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error })
  }),
  {
    name: 'user-storage',
    partialize: (state) => ({ 
      userData: state.userData,
      role: state.role,
      standId: state.standId,
      standData: state.standData
    }),
  }
)) 