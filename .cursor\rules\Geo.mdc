---
description: 
globs: 
alwaysApply: true
---
CONFIGURATION: n = 3 files
INSTRUCTIONS
Look at the app design that has been attached.
Create n different versions of that same app, each optimized for a different geographical region.
Each version should have the same functionality but be tailored for different locations like:


North America (US/Canada)
Europe (EU regions)
Asia Pacific (Japan, Korea, China, etc.)
Middle East (UAE, Saudi Arabia, etc.)
Latin America (Mexico, Brazil, Argentina, etc.)
Africa (South Africa, Nigeria, etc.)
India (specific cultural preferences)
Nordic Countries (Sweden, Norway, Denmark)
Urban vs Rural (same country, different environments)


Take any additional geographical instructions from the user prompt into account.
FILE MANAGEMENT



Create a "variations3" folder
Put all the geographical variations inside it


CREATE THE GEOGRAPHICAL DESIGNS
Make n design files:


design1.html (for region 1)
design2.html (for region 2)
design3.html (for region 3)
Continue up to design{n}.html


Each design should be optimized for a different geographical region.
WHAT GOES IN THE FOLDER
/variations/
├── design1.html
├── design2.html
├── design3.html
└── design{n}.html
GEOGRAPHICAL ADAPTATION RULES


Keep the same app functionality
Adapt colors to cultural preferences and meanings
Adjust text direction (left-to-right vs right-to-left)
Modify layouts for cultural reading patterns
Change imagery and icons to be culturally appropriate
Adapt to local legal requirements (GDPR, privacy laws)
Consider local internet infrastructure and speed
Follow regional design trends and preferences


CULTURAL CONSIDERATIONS


Colors: Red means luck in China, danger in West; White means purity in West, mourning in East
Text Direction: Arabic/Hebrew (right-to-left), Asian (vertical options)
Images: Avoid culturally sensitive imagery, use appropriate representations
Data Formats: Date formats (MM/DD vs DD/MM), currency symbols, number formatting
Privacy: GDPR compliance for EU, different privacy expectations by region


REGIONAL DESIGN PATTERNS


Western: Clean, minimal, lots of white space

Asian: Information-dense, vibrant colors, detailed interfaces
Middle Eastern: Right-to-left layouts, ornate design elements
Nordic: Clean, functional, accessibility-focused
Latin American: Warm colors, community-focused features


EXAMPLE
If given a to-do list app:


US version: Clean design, productivity-focused, time management features
Japanese version: Detailed categories, group collaboration, respect for hierarchy
Middle Eastern version: Right-to-left text, family/community task sharing, prayer time integration


