import React, { useEffect } from 'react';
import { FaInstagram } from 'react-icons/fa';
import styled from 'styled-components';
import { useLanguageStore } from '../store/useLanguageStore';

const WidgetContainer = styled.div`
  width: 100%;
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const IconCircle = styled.div`
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #f9ce34 0%, #ee2a7b 50%, #6228d7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
`;

const InstaTitle = styled.h2`
  font-size: 1.7rem;
  color: #e86a1a;
  font-weight: 800;
  text-align: center;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
`;

const InstagramWidget = () => {
  const { language } = useLanguageStore();

  useEffect(() => {
    // Solo agrega el script si no existe ya
    if (!document.querySelector('script[src="https://cdn.lightwidget.com/widgets/lightwidget.js"]')) {
      const script = document.createElement('script');
      script.src = 'https://cdn.lightwidget.com/widgets/lightwidget.js';
      script.async = true;
      document.body.appendChild(script);
    }
  }, []);

  return (
    <WidgetContainer>
      <IconCircle>
        <FaInstagram size={32} color="#fff" />
      </IconCircle>
      <InstaTitle>
        {language === 'es'
          ? 'Instagram Mercado Food Hall & Shops'
          : 'Instagram Mercado Food Hall & Shops'}
      </InstaTitle>
      <iframe
        src="https://cdn.lightwidget.com/widgets/b3ecc7b090975b798eb0f158e15b91e0.html"
        scrolling="no"
        allowTransparency="true"
        className="lightwidget-widget"
        style={{ width: '100%', border: 0, overflow: 'hidden' }}
        title="Instagram Feed"
      ></iframe>
    </WidgetContainer>
  );
};

export default InstagramWidget; 