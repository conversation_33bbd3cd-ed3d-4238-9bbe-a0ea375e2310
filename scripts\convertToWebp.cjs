/**
 * Batch-convert every .jpg, .jpeg and .png under /public → .webp
 *
 *   – Retains the original file name (only extension changes)
 *   – Skips files that already have a .webp sibling
 *   – Ignores .svg, .ico, .mp4 and existing .webp files
 *
 * Usage:  npm run img:webp
 */
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Root directory of the repo
const ROOT = path.resolve(__dirname, '..');
const PUBLIC_DIR = path.join(ROOT, 'public');

// Adjust as desired (0-100). 80 is a good balance.
const QUALITY = 80;

/**
 * Traverse the directory tree depth-first, processing JPG/PNG files.
 * @param {string} dir Absolute directory path
 */
function walk(dir) {
  for (const entry of fs.readdirSync(dir)) {
    const absPath = path.join(dir, entry);
    const stat = fs.statSync(absPath);
    if (stat.isDirectory()) {
      walk(absPath);
      continue;
    }

    const ext = path.extname(entry).toLowerCase();
    if (!['.jpg', '.jpeg', '.png'].includes(ext)) continue;

    const outPath = absPath.replace(ext, '.webp');

    // Skip if a WebP already exists
    if (fs.existsSync(outPath)) continue;

    sharp(absPath)
      .webp({ quality: QUALITY })
      .toFile(outPath)
      .then(() => {
        console.log('✔ Converted', path.relative(PUBLIC_DIR, outPath));
      })
      .catch((err) => {
        console.error('✖ Failed', absPath, err);
      });
  }
}

(async () => {
  console.log('Converting images in', PUBLIC_DIR);
  walk(PUBLIC_DIR);
})(); 