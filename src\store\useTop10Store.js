import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

export const useTop10Store = create((set) => ({
  top10Stands: [],
  loading: false,
  error: null,

  fetchTop10Stands: async () => {
    set({ loading: true, error: null });
    try {
      // Fetch the top 10 from the top_10 table
      const { data: top10, error: errorTop10 } = await supabase
        .from('top_10')
        .select('*')
        .order('number', { ascending: true })
        .limit(10);

      if (errorTop10) throw errorTop10;

      // Get the stands using the id_local from top_10
      const ids = (top10 || []).map((row) => row.id_local);
      if (ids.length === 0) {
        set({ top10Stands: [], loading: false });
        return;
      }

      const { data: stands, error: errorStands } = await supabase
        .from('stands')
        .select(`
          id,
          name,
          banner_url,
          avatar,
          slug,
          category,
          description,
          email,
          whatsapp,
          facebook,
          instagram
        `)
        .in('id', ids);

      if (errorStands) throw errorStands;

      // Order the stands according to the top_10 ranking
      const orderedStands = top10.map(topItem => {
        const stand = stands.find(s => s.id === topItem.id_local);
        return stand ? { ...stand, ranking: topItem.number } : null;
      }).filter(Boolean);

      set({ top10Stands: orderedStands, loading: false });
    } catch (error) {
      console.error('Error fetching top 10 stands:', error);
      set({ top10Stands: [], error: error.message, loading: false });
    }
  },
})); 