---
description: 
globs: 
alwaysApply: true
---
# Database: Create RLS policies

You're a Supabase Postgres expert in writing Row Level Security (RLS) policies. Generate **secure and efficient RLS policies** that adhere to the following best practices:

## General Guidelines

1. **Enable RLS First:**
   - Always enable RLS on tables containing sensitive data
   - Use `ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;`

2. **Principle of Least Privilege:**
   - Grant only the minimum necessary access
   - Be explicit about what each policy allows

3. **Authentication Checks:**
   - Use `auth.uid()` to get the current user's ID
   - Use `auth.role()` to check user roles
   - Always check for authenticated users when needed

## Best Practices

1. **Policy Naming:**
   - Use descriptive names that explain what the policy does
   - Include the operation type (SELECT, INSERT, UPDATE, DELETE)

2. **Performance Considerations:**
   - Write efficient policy expressions
   - Use indexes on columns referenced in policies
   - Avoid complex subqueries when possible

3. **Security Considerations:**
   - Always validate user ownership
   - Consider data sensitivity levels
   - Test policies thoroughly

## Common Policy Patterns

### Public Read Access

```sql
-- Allow everyone to read public data
CREATE POLICY "Public profiles are viewable by everyone" 
    ON public.profiles 
    FOR SELECT 
    USING (true);
```

### User-Specific Access

```sql
-- Users can only access their own data
CREATE POLICY "Users can view their own profile" 
    ON public.profiles 
    FOR SELECT 
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
    ON public.profiles 
    FOR UPDATE 
    USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" 
    ON public.profiles 
    FOR INSERT 
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can delete their own profile" 
    ON public.profiles 
    FOR DELETE 
    USING (auth.uid() = id);
```

### Role-Based Access

```sql
-- Admin users can access all data
CREATE POLICY "Admins can view all profiles" 
    ON public.profiles 
    FOR ALL 
    USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() 
            AND r.name = 'admin'
        )
    );
```

### Conditional Access

```sql
-- Users can read published posts or their own drafts
CREATE POLICY "Users can view published posts and own drafts" 
    ON public.posts 
    FOR SELECT 
    USING (
        status = 'published' 
        OR author_id = auth.uid()
    );
```

### Team/Organization Access

```sql
-- Users can access data from their organization
CREATE POLICY "Users can view team data" 
    ON public.projects 
    FOR SELECT 
    USING (
        team_id IN (
            SELECT team_id 
            FROM public.team_members 
            WHERE user_id = auth.uid()
        )
    );
```

## Example Policy Sets

### User Profiles Table

```sql
-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Public read access to basic profile info
CREATE POLICY "Public profiles are viewable by everyone" 
    ON public.profiles 
    FOR SELECT 
    USING (true);

-- Users can insert their own profile
CREATE POLICY "Users can insert their own profile" 
    ON public.profiles 
    FOR INSERT 
    WITH CHECK (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile" 
    ON public.profiles 
    FOR UPDATE 
    USING (auth.uid() = id);

-- Users cannot delete profiles (or add separate policy if needed)
-- CREATE POLICY "Users can delete their own profile" 
--     ON public.profiles 
--     FOR DELETE 
--     USING (auth.uid() = id);
```

### Private Posts Table

```sql
-- Enable RLS
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;

-- Users can view published posts and their own posts
CREATE POLICY "Users can view published posts and own posts" 
    ON public.posts 
    FOR SELECT 
    USING (
        status = 'published' 
        OR author_id = auth.uid()
    );

-- Users can insert their own posts
CREATE POLICY "Users can insert their own posts" 
    ON public.posts 
    FOR INSERT 
    WITH CHECK (auth.uid() = author_id);

-- Users can update their own posts
CREATE POLICY "Users can update their own posts" 
    ON public.posts 
    FOR UPDATE 
    USING (auth.uid() = author_id);

-- Users can delete their own posts
CREATE POLICY "Users can delete their own posts" 
    ON public.posts 
    FOR DELETE 
    USING (auth.uid() = author_id);
```

### Comments Table with Moderation

```sql
-- Enable RLS
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Everyone can read approved comments
CREATE POLICY "Everyone can view approved comments" 
    ON public.comments 
    FOR SELECT 
    USING (status = 'approved');

-- Users can view their own comments regardless of status
CREATE POLICY "Users can view their own comments" 
    ON public.comments 
    FOR SELECT 
    USING (author_id = auth.uid());

-- Users can insert comments (pending approval)
CREATE POLICY "Users can insert comments" 
    ON public.comments 
    FOR INSERT 
    WITH CHECK (
        auth.uid() = author_id 
        AND status = 'pending'
    );

-- Users can update their own pending comments
CREATE POLICY "Users can update their own pending comments" 
    ON public.comments 
    FOR UPDATE 
    USING (
        auth.uid() = author_id 
        AND status = 'pending'
    );

-- Moderators can update comment status
CREATE POLICY "Moderators can update comment status" 
    ON public.comments 
    FOR UPDATE 
    USING (
        EXISTS (
            SELECT 1 FROM public.user_roles ur
            JOIN public.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() 
            AND r.name IN ('moderator', 'admin')
        )
    );
```

### Storage Policies

```sql
-- Users can view their own files
CREATE POLICY "Users can view their own files" 
    ON storage.objects 
    FOR SELECT 
    USING (
        bucket_id = 'user-files' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can upload to their own folder
CREATE POLICY "Users can upload to their own folder" 
    ON storage.objects 
    FOR INSERT 
    WITH CHECK (
        bucket_id = 'user-files' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can update their own files
CREATE POLICY "Users can update their own files" 
    ON storage.objects 
    FOR UPDATE 
    USING (
        bucket_id = 'user-files' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can delete their own files
CREATE POLICY "Users can delete their own files" 
    ON storage.objects 
    FOR DELETE 
    USING (
        bucket_id = 'user-files' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );
```

## Testing Policies

Always test your policies with different user scenarios:

```sql
-- Test as specific user
SELECT * FROM public.posts; -- Should only return user's posts + published posts

-- Test policy logic
SELECT 
    *,
    (status = 'published' OR author_id = auth.uid()) as policy_check
FROM public.posts;
```

