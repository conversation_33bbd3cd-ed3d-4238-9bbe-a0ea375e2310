import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

export const useTenantListingsStore = create((set, get) => ({
  tenants: [],
  loading: false,
  error: null,

  fetchTenants: async () => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('tenant_listings')
        .select('*')
        .order('tenant_name', { ascending: true });
      if (error) throw error;
      set({ tenants: data || [], loading: false });
    } catch {
      set({ error: 'Error loading tenant listings', loading: false });
    }
  },

  createTenant: async (tenantData) => {
    try {
      set({ loading: true, error: null });

      // Ensure units is properly formatted as an array
      const formattedData = {
        ...tenantData,
        units: Array.isArray(tenantData.units)
          ? tenantData.units.filter(unit => unit && unit.trim() !== '')
          : tenantData.units ? [tenantData.units.toString().trim()] : []
      };

      const { data, error } = await supabase
        .from('tenant_listings')
        .insert([formattedData])
        .select()
        .single();

      if (error) throw error;

      // Add the new tenant to the store
      const currentTenants = get().tenants;
      set({
        tenants: [...currentTenants, data].sort((a, b) => a.tenant_name.localeCompare(b.tenant_name)),
        loading: false
      });

      return { data, error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { data: null, error: error.message };
    }
  },

  updateTenant: async (id, tenantData) => {
    try {
      set({ loading: true, error: null });

      // Ensure units is properly formatted as an array
      const formattedData = {
        ...tenantData,
        units: Array.isArray(tenantData.units)
          ? tenantData.units.filter(unit => unit && unit.trim() !== '')
          : tenantData.units ? [tenantData.units.toString().trim()] : []
      };

      const { data, error } = await supabase
        .from('tenant_listings')
        .update(formattedData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update the tenant in the store
      const currentTenants = get().tenants;
      const updatedTenants = currentTenants.map(tenant =>
        tenant.id === id ? data : tenant
      ).sort((a, b) => a.tenant_name.localeCompare(b.tenant_name));

      set({ tenants: updatedTenants, loading: false });

      return { data, error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { data: null, error: error.message };
    }
  },

  deleteTenant: async (id) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('tenant_listings')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Remove the tenant from the store
      const currentTenants = get().tenants;
      const filteredTenants = currentTenants.filter(tenant => tenant.id !== id);

      set({ tenants: filteredTenants, loading: false });

      return { error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { error: error.message };
    }
  },

  clearError: () => set({ error: null }),
}));