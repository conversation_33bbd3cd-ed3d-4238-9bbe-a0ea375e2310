const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- VITE_SUPABASE_URL (or SUPABASE_URL)');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Utility functions
const slugify = (str) => str.toString().toLowerCase().trim().replace(/[^a-z0-9]+/g,'-').replace(/^-+|-+$/g,'');

const parseUnits = (unitString) => {
  if (!unitString || unitString.trim() === '') {
    return [];
  }

  // Handle comma-separated units and clean them up
  return unitString
    .split(',')
    .map(unit => unit.trim())
    .filter(unit => unit !== '');
};

const deriveCategory = (unitType) => {
  if (!unitType) return 'Retail';
  const lowerType = unitType.toLowerCase();
  return lowerType.includes('beauty') ? 'Beauty' : 'Retail';
};

// Main import function
async function importTenantListings() {
  const csvPath = process.argv[2] || './tenant-listings.csv';

  if (!fs.existsSync(csvPath)) {
    console.error(`CSV file not found: ${csvPath}`);
    console.log('Usage: npm run import-tenants [path-to-csv]');
    process.exit(1);
  }

  console.log(`Reading CSV file: ${csvPath}`);

  try {
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').filter(line => line.trim() !== '');

    if (lines.length === 0) {
      console.log('CSV file is empty');
      return;
    }

    // Skip header row(s) - adjust as needed based on your CSV format
    const dataLines = lines.slice(1);
    const tenants = [];

    console.log(`Processing ${dataLines.length} rows...`);

    for (let i = 0; i < dataLines.length; i++) {
      const line = dataLines[i];

      // Parse CSV line (assuming semicolon-delimited based on documentation)
      const columns = line.split(';').map(col => col.trim().replace(/^"|"$/g, ''));

      // Adjust column indices based on your CSV structure
      // Expected format: Tenant Name; Unit; Unit Type; Phone; etc.
      const [tenantName, unit, unitType, phone, ...rest] = columns;

      if (!tenantName || tenantName.trim() === '') {
        console.log(`Skipping row ${i + 2}: Missing tenant name`);
        continue;
      }

      const slug = slugify(tenantName);
      const category = deriveCategory(unitType);
      const units = parseUnits(unit);

      const tenantData = {
        tenant_name: tenantName.trim(),
        unit: unit?.trim() || null, // Keep legacy field for compatibility
        units: units, // New array field
        unit_type: unitType?.trim() || null,
        phone_default: phone?.trim() || null,
        category: category,
        slug: slug
      };

      tenants.push(tenantData);
    }

    console.log(`Parsed ${tenants.length} valid tenant records`);

    if (tenants.length === 0) {
      console.log('No valid tenant data to import');
      return;
    }

    // Upsert tenants into database
    console.log('Upserting tenants into database...');

    const { data, error } = await supabase
      .from('tenant_listings')
      .upsert(tenants, {
        onConflict: 'tenant_name',
        ignoreDuplicates: false
      })
      .select();

    if (error) {
      console.error('Database error:', error);
      process.exit(1);
    }

    console.log(`Successfully imported ${data?.length || tenants.length} tenant listings`);

    // Log some statistics
    const retailCount = tenants.filter(t => t.category === 'Retail').length;
    const beautyCount = tenants.filter(t => t.category === 'Beauty').length;
    const multiUnitCount = tenants.filter(t => t.units.length > 1).length;

    console.log('\nImport Statistics:');
    console.log(`- Total tenants: ${tenants.length}`);
    console.log(`- Retail tenants: ${retailCount}`);
    console.log(`- Beauty tenants: ${beautyCount}`);
    console.log(`- Multi-unit tenants: ${multiUnitCount}`);

  } catch (error) {
    console.error('Import failed:', error);
    process.exit(1);
  }
}

// Run the import
if (require.main === module) {
  importTenantListings()
    .then(() => {
      console.log('Import completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}