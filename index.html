<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- SEO: Descriptive summary shown in search results -->
    <meta name="description" content="Mercado Food Hall & Shops – discover the best restaurants, shops, beauty services and events in Chihuahua's most vibrant marketplace." />
    
    <!-- Optimized font loading - preconnect for faster DNS resolution -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Critical fonts preloaded with font-display: swap -->
    <link rel="preload" as="video" href="/video.mp4" type="video/mp4" crossorigin fetchpriority="high">
    <link 
      rel="preload" 
      as="style" 
      href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <link 
      rel="preload" 
      as="style" 
      href="https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    
    <!-- Fallback for non-JS browsers -->
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap" rel="stylesheet">
    </noscript>
    
    <title>Mecrado</title>
    <link rel="preload" as="image" href="/people/1Q1A3253.webp" />
    <link rel="preload" as="image" href="/people/1Q1A3269.webp" />
    <!-- Preload LCP poster image with highest priority -->
    <link rel="preload" as="image" href="/banner-mall.webp" type="image/webp" fetchpriority="high">
  </head>
  <body>
    <div id="root"></div>
    <!-- Preload main JS module to shorten critical request chain -->
    <link rel="modulepreload" href="/src/main.jsx" />

    <!-- Inline small critical global CSS to avoid extra request -->
    <style>
      :root {
        font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-synthesis: none;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-display: swap;
      }

      .bebas-neue {
        font-family: "Bebas Neue", "Arial Black", "Helvetica Bold", sans-serif;
        font-display: swap;
      }

      [style*="Bebas Neue"] {
        font-family: "Bebas Neue", "Arial Black", "Helvetica Bold", sans-serif !important;
        font-display: swap;
      }

      @font-face {
        font-family: 'Open Sans';
        font-display: swap;
        src: local('Open Sans'), local('OpenSans');
      }

      @font-face {
        font-family: 'Bebas Neue';
        font-display: swap;
        src: local('Bebas Neue'), local('BebasNeue');
      }
    </style>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
