import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUserStore } from '../../store/useUserStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';
import { toast } from 'react-hot-toast';
import { Gift } from 'lucide-react';

const Container = styled(motion.div)`
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
`;

const Form = styled.form`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const PointsSelector = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const PointButton = styled(motion.button)`
  padding: 0.5rem 1rem;
  border: 2px solid #D8DF20;
  border-radius: 6px;
  background: ${props => props.selected ? '#D8DF20' : 'white'};
  color: ${props => props.selected ? 'white' : '#2c3e50'};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.selected ? '#D8DF20' : '#f8f9fa'};
  }
`;

const SubmitButton = styled(motion.button)`
  background: #D8DF20;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  margin-top: 1rem;

  &:disabled {
    background: #e9ecef;
    cursor: not-allowed;
  }
`;

const Points = () => {
  const { standId } = useUserStore();
  const { language } = useLanguageStore();
  const [email, setEmail] = useState('');
  const [selectedPoints, setSelectedPoints] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!standId) {
      toast.error(language === 'es' ? 'No se encontró el ID del stand' : 'Stand ID not found');
      return;
    }

    if (!email || !selectedPoints) {
      toast.error(language === 'es' ? 'Por favor completa todos los campos' : 'Please complete all fields');
      return;
    }

    try {
      setLoading(true);

      // 1. Buscar el usuario por email
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, points')
        .eq('email', email)
        .single();

      if (userError) {
        throw new Error(language === 'es' ? 'No se encontró el usuario' : 'User not found');
      }

      // 2. Verificar si ya se le dieron puntos
      const { data: existingPoints, error: pointsError } = await supabase
        .from('stand_points')
        .select('id')
        .eq('stand_id', standId)
        .eq('user_id', userData.id)
        .maybeSingle();

      if (pointsError) {
        throw pointsError;
      }

      if (existingPoints) {
        throw new Error(language === 'es' ? 'Ya has dado puntos a este usuario' : 'You have already given points to this user');
      }

      // 3. Insertar los puntos en stand_points
      const { error: insertError } = await supabase
        .from('stand_points')
        .insert({
          stand_id: standId,
          user_id: userData.id,
          points: selectedPoints
        });

      if (insertError) {
        throw insertError;
      }

      // 4. Actualizar los puntos del usuario
      const { error: updateError } = await supabase
        .from('users')
        .update({ 
          points: (userData.points || 0) + selectedPoints 
        })
        .eq('id', userData.id);

      if (updateError) {
        throw updateError;
      }

      toast.success(language === 'es' ? '¡Puntos asignados exitosamente!' : 'Points assigned successfully!');
      setEmail('');
      setSelectedPoints(0);
    } catch (error) {
      console.error('Error al asignar puntos:', error);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Title>
        <Gift size={24} style={{ marginRight: '0.5rem' }} />
        {language === 'es' ? 'Asignar Puntos' : 'Assign Points'}
      </Title>

      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>{language === 'es' ? 'Correo Electrónico del Usuario' : 'User Email Address'}</Label>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={language === 'es' ? '<EMAIL>' : '<EMAIL>'}
            required
          />
        </FormGroup>

        <FormGroup>
          <Label>{language === 'es' ? 'Cantidad de Puntos' : 'Points Amount'}</Label>
          <PointsSelector>
            {[10, 20, 30, 50, 100].map((points) => (
              <PointButton
                key={points}
                type="button"
                selected={selectedPoints === points}
                onClick={() => setSelectedPoints(points)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {points} pts
              </PointButton>
            ))}
          </PointsSelector>
        </FormGroup>

        <SubmitButton
          type="submit"
          disabled={loading || !email || !selectedPoints}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {loading 
            ? (language === 'es' ? 'Asignando...' : 'Assigning...') 
            : (language === 'es' ? 'Asignar Puntos' : 'Assign Points')
          }
        </SubmitButton>
      </Form>
    </Container>
  );
};

export default Points; 