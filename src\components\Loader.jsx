import React from 'react';
import styled from 'styled-components';

const LoaderContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
`;

const Logo = styled.img`
  width: 200px;
  height: auto;
  animation: pulse 1.5s ease-in-out infinite;

  @keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
  }
`;

const Loader = () => {
  return (
    <LoaderContainer>
      <Logo src="/logo-2.webp" alt="Cargando..." loading="lazy" />
    </LoaderContainer>
  );
};

export default Loader; 