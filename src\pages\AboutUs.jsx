import React, { useState } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Footer } from '../components/Footer';
import { useLanguageStore } from '../store/useLanguageStore';

const PageWrapper = styled.div`
  background-color: #ffffff;
  min-height: 100vh;
`;

const Container = styled.div`
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  
  @media (max-width: 768px) {
    padding: 1.5rem 0.8rem 2.5rem 0.8rem;
  }
  
  @media (max-width: 480px) {
    padding: 1rem 0.5rem 2rem 0.5rem;
  }
`;

const Title = styled.h1`
  font-size: 2.8rem;
  font-weight: 900;
  color: #222;
  margin-bottom: 1.5rem;
  border-left: 8px solid #F16925;
  padding-left: 1rem;
  letter-spacing: 2px;
  @media (max-width: 600px) {
    font-size: 2rem;
    padding-left: 0.5rem;
    border-left-width: 4px;
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  align-items: flex-start;
  
  @media (max-width: 900px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  @media (max-width: 768px) {
    gap: 1.5rem;
  }
  
  @media (max-width: 480px) {
    gap: 1rem;
  }
`;

const ImageGallery = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 0.8rem 1rem;
  
  @media (max-width: 900px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 0.7rem 0.8rem;
  }
  
  @media (max-width: 768px) {
    gap: 0.6rem;
  }
  
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
    padding-bottom: 1rem;
  }
  
  @media (max-width: 480px) {
    gap: 1.2rem;
  }

  /* Style the div containers */
  > div {
    display: flex;
    flex-direction: column;
  }

  /* Make the full image span both columns */
  > img {
    grid-column: 1 / span 2;
    
    @media (max-width: 600px) {
      grid-column: 1 / span 1;
    }
  }
`;

const ImageLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 700;
  color: #F16925;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 0.5rem;
  margin-bottom: 0.3rem;
  text-align: center;
  
  @media (max-width: 600px) {
    font-size: 0.8rem;
    margin-top: 0.4rem;
    margin-bottom: 0.2rem;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
  
  @media (max-width: 480px) {
    padding: 1rem;
    align-items: center;
    justify-content: center;
  }
`;

const ModalContent = styled.div`
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 768px) {
    max-width: 95vw;
    max-height: 85vh;
  }
  
  @media (max-width: 480px) {
    max-width: 90vw;
    max-height: 85vh;
    justify-content: center;
  }
`;

const ModalImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  
  @media (max-width: 768px) {
    max-height: 65vh;
    border-radius: 8px;
  }
  
  @media (max-width: 480px) {
    max-height: 55vh;
    border-radius: 6px;
  }
`;

const ModalCaption = styled.p`
  color: white;
  font-size: 1.2rem;
  margin-top: 1rem;
  text-align: center;
  font-weight: 500;
  padding: 0 1rem;
  
  @media (max-width: 768px) {
    font-size: 1rem;
    margin-top: 0.8rem;
    padding: 0 0.5rem;
  }
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin-top: 0.6rem;
    padding: 0 0.25rem;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
  
  @media (max-width: 768px) {
    top: -55px;
    right: 5px;
    width: 44px;
    height: 44px;
    font-size: 1.6rem;
  }
  
  @media (max-width: 480px) {
    top: -60px;
    right: 0;
    width: 48px;
    height: 48px;
    font-size: 1.8rem;
    background: rgba(255, 255, 255, 0.3);
  }
`;

const NavigationButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  ${props => props.direction === 'left' ? 'left: -70px;' : 'right: -70px;'}
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  @media (max-width: 768px) {
    ${props => props.direction === 'left' ? 'left: -60px;' : 'right: -60px;'}
    width: 44px;
    height: 44px;
    font-size: 1.3rem;
  }
  
  @media (max-width: 480px) {
    ${props => props.direction === 'left' ? 'left: 5px;' : 'right: 5px;'}
    width: 48px;
    height: 48px;
    font-size: 1.4rem;
    background: rgba(255, 255, 255, 0.3);
    top: 50%;
    transform: translateY(-50%);
    
    &:hover {
      transform: translateY(-50%) scale(1.05);
    }
  }
`;

const GalleryImg = styled.img`
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-radius: 10px;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
  
  @media (max-width: 900px) {
    height: 150px;
  }
  
  @media (max-width: 768px) {
    border-radius: 8px;
    height: 140px;
    
    &:hover {
      transform: scale(1.01);
    }
  }
  
  @media (max-width: 600px) {
    height: 200px;
  }
  
  @media (max-width: 480px) {
    height: 180px;
  }
`;

const GalleryImgFull = styled.img`
  width: 100%;
  height: auto;
  max-height: 320px;
  object-fit: contain;
  border-radius: 10px;
  grid-column: 1 / span 2;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.01);
  }
  
  @media (max-width: 768px) {
    border-radius: 8px;
    max-height: 280px;
  }
  
  @media (max-width: 600px) {
    grid-column: 1 / span 1;
    max-height: 240px;
  }
  
  @media (max-width: 480px) {
    max-height: 200px;
  }
`;

const TextSection = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  @media (max-width: 900px) {
    padding: 1rem 0.5rem 0 0.5rem;
  }
  
  @media (max-width: 768px) {
    padding: 0.8rem 0.3rem 0 0.3rem;
  }
  
  @media (max-width: 600px) {
    padding: 1rem 0.2rem 0 0.2rem;
  }
  
  @media (max-width: 480px) {
    padding: 0.5rem 0.1rem 0 0.1rem;
  }
`;

const Paragraph = styled.p`
  color: #444;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.2rem;
  text-align: justify;
  
  @media (max-width: 768px) {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    text-align: left;
  }
  
  @media (max-width: 600px) {
    font-size: 0.95rem;
    margin-bottom: 0.9rem;
  }
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.8rem;
  }
`;

const Highlight = styled.span`
  font-weight: bold;
  color: #F16925;
`;

const AboutUs = () => {
  const { language } = useLanguageStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const images = [
    {
      src: '/about2.webp',
      caption: language === 'es' ? 'Mercado Food Hall - Después' : 'Mercado Food Hall - After',
      alt: 'Mercado Food Hall'
    },
    {
      src: '/about1-3.webp',
      caption: language === 'es' ? 'Bargain Bazaar - Antes' : 'Bargain Bazaar - Before',
      alt: 'Bargain Bazaar'
    },
    {
      src: '/about5.webp',
      caption: language === 'es' ? 'Nuestros Fundadores' : 'Our Founders',
      alt: 'Owners'
    }
  ];

  const content = {
    title: language === 'es' ? 'ESTE ES MERCADO FOOD HALL' : 'THIS IS MERCADO FOOD HALL',
    paragraph1: language === 'es' 
      ? 'En 1983, Bargain Bazaar se establece como el único mercado de pulgas interior con espacios para vendedores locales durante más de 35 años. En 2019, es adquirido por Jesús González.'
      : 'In 1983, Bargain Bazaar is established as the only indoor flea market with spaces for local vendors for over 35 years. In 2019, it is acquired by Jesús González.',
    paragraph2: language === 'es'
      ? 'En 2021, Bargain Bazaar renace como Mercado Food Hall (también conocido como Mercado District) con un concepto único en el Valle de Texas inspirado en grandes mercados urbanos alrededor del mundo como Chelsea Market en Nueva York, El Mercado de San Miguel en Madrid o Mercado Roma en la Ciudad de México.'
      : 'In 2021 Bargain Bazaar is reborn as Mercado Food Hall (also known as Mercado District) with a unique concept in the Texas Valley inspired by great urban markets around the world such as Chelsea Market in New York, El Mercado de San Miguel in Madrid or Mercado Roma in Mexico City.',
    paragraph3: language === 'es'
      ? 'Actualmente, Mercado District cuenta con más de 212 suites creadas para emprendedores, chefs, artesanos, profesionales de la belleza y marcas locales establecidas.'
      : 'Currently, Mercado District has more than 212 suites created for entrepreneurs, chefs, artisans, beauty professionals and established local brands.',
    paragraph4: language === 'es'
      ? 'Esta transformación responde al deseo de ofrecer una experiencia cultural, gastronómica y comercial que combine el talento local con un entorno moderno, vibrante y accesible.'
      : 'This transformation responds to the desire to offer a cultural, gastronomic and commercial experience that combines local talent with a modern, vibrant and accessible environment.',
    paragraph5: language === 'es'
      ? 'Nuestro objetivo es claro: convertirnos en una parada obligada para los visitantes de McAllen y el Valle del Río Grande y al mismo tiempo ser un semillero para emprendedores que buscan hacer las cosas de manera diferente, innovadora y auténtica. Mercado es más que un lugar para comprar, comer y relajarse; es un lugar para descubrir, compartir y crear experiencias.'
      : 'Our goal is clear: to become a must stop for guests to McAllen and the Rio Grande Valley and at the same time be a hotbed for entrepreneurs looking to do things differently, innovatively and authentically. Mercado is more than just a place to shop, dine and relax; it\'s a place to discover, share and create experiences.'
  };

  const openModal = (imageIndex) => {
    setSelectedImageIndex(imageIndex);
    setIsModalOpen(true);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = React.useCallback(() => {
    setIsModalOpen(false);
    setSelectedImageIndex(0);
    // Restore body scroll
    document.body.style.overflow = 'unset';
  }, []);

  const nextImage = React.useCallback(() => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = React.useCallback(() => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  // Handle keyboard navigation
  React.useEffect(() => {
    const handleKeyPress = (e) => {
      if (isModalOpen) {
        if (e.key === 'Escape') {
          closeModal();
        } else if (e.key === 'ArrowRight') {
          nextImage();
        } else if (e.key === 'ArrowLeft') {
          prevImage();
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isModalOpen, nextImage, prevImage, closeModal]);

  // Handle touch/swipe gestures for mobile
  React.useEffect(() => {
    if (!isModalOpen) return;

    let startX = 0;
    let startY = 0;

    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e) => {
      if (!startX || !startY) return;
      
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      
      const diffX = startX - endX;
      const diffY = startY - endY;
      
      // Only handle horizontal swipes if they're significantly more than vertical
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0) {
          nextImage(); // Swipe left = next image
        } else {
          prevImage(); // Swipe right = previous image
        }
      }
      
      startX = 0;
      startY = 0;
    };

    const modalElement = document.querySelector('[data-modal]');
    if (modalElement) {
      modalElement.addEventListener('touchstart', handleTouchStart, { passive: true });
      modalElement.addEventListener('touchend', handleTouchEnd, { passive: true });
      
      return () => {
        modalElement.removeEventListener('touchstart', handleTouchStart);
        modalElement.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isModalOpen, nextImage, prevImage]);

  return (
    <>
      <Navbar />
      <PageWrapper>
        <Container>
          <Title>{content.title}</Title>
          <Grid>
            <ImageGallery>
              <div>
                <GalleryImg 
                  src={images[0].src} 
                  alt={images[0].alt} 
                  loading="lazy" 
                  onClick={() => openModal(0)} 
                />
                <ImageLabel>{language === 'es' ? 'Antes' : 'Before'}</ImageLabel>
              </div>
              <div>
                <GalleryImg 
                  src={images[1].src} 
                  alt={images[1].alt} 
                  loading="lazy" 
                  onClick={() => openModal(1)} 
                />
                <ImageLabel>{language === 'es' ? 'Después' : 'After'}</ImageLabel>
              </div>
              <GalleryImgFull 
                src={images[2].src} 
                alt={images[2].alt} 
                loading="lazy" 
                onClick={() => openModal(2)} 
              />
            </ImageGallery>
            <TextSection>
              <Paragraph>
                <Highlight>In 1983</Highlight> {content.paragraph1}
              </Paragraph>
              <Paragraph>
                <Highlight>In 2021</Highlight> {content.paragraph2}
              </Paragraph>
              <Paragraph>
                {content.paragraph3}
              </Paragraph>
              <Paragraph>
                {content.paragraph4}
              </Paragraph>
              <Paragraph>
                {content.paragraph5}
              </Paragraph>
            </TextSection>
          </Grid>
        </Container>
        <Footer />
      </PageWrapper>
      {isModalOpen && (
        <Modal onClick={closeModal} data-modal>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <CloseButton onClick={closeModal}>×</CloseButton>
            <NavigationButton 
              direction="left" 
              onClick={prevImage}
              disabled={images.length <= 1}
            >
              ←
            </NavigationButton>
            <NavigationButton 
              direction="right" 
              onClick={nextImage}
              disabled={images.length <= 1}
            >
              →
            </NavigationButton>
            <ModalImage 
              src={images[selectedImageIndex].src} 
              alt={images[selectedImageIndex].alt} 
            />
            <ModalCaption>
              {images[selectedImageIndex].caption}
            </ModalCaption>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default AboutUs; 