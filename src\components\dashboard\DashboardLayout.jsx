import React from 'react';
import { Outlet } from 'react-router-dom';
import styled from 'styled-components';
import Sidebar from './Sidebar';
import TopNavbar from './TopNavbar';
import MobileMenu from './MobileMenu';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: #f4f4f9;
  position: relative;
`;

const SidebarWrapper = styled.div`
  width: 15%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1001;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    display: none;
  }
`;

const MainWrapper = styled.div`
  flex: 1;
  margin-left: 15%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f4f4f9;
  position: relative;

  @media (max-width: 768px) {
    margin-left: 0;
    width: 100%;
  }
`;

const TopNavbarWrapper = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  left: 15%;
  height: 60px;
  z-index: 999;
  background: white;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    left: 0;
  }
`;

const MainContent = styled.main`
  flex: 1;
  padding: 2rem;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
  width: 100%;
  max-width: 1400px;
  margin: 60px auto 0;
`;

const DashboardLayout = () => {
  return (
    <LayoutContainer>
      <SidebarWrapper>
        <Sidebar />
      </SidebarWrapper>
      <MainWrapper>
        <TopNavbarWrapper>
          <TopNavbar />
        </TopNavbarWrapper>
        <MobileMenu />
        <MainContent>
          <Outlet />
        </MainContent>
      </MainWrapper>
    </LayoutContainer>
  );
};

export default DashboardLayout; 