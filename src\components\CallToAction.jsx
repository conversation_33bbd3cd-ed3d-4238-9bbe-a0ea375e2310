import React from 'react';
import styled from 'styled-components';
import { useLanguageStore } from '../store/useLanguageStore';

const FormSection = styled.section`
  width: 100%;
  padding: 6rem 0;
  background-image: url('/mark.webp');
  background-size: cover;
  background-position: center;  
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }
`;

const FormContainer = styled.div`
  background: white;
  padding: 3rem 2.5rem;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  text-align: left;
  position: relative;
  z-index: 2;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  @media (max-width: 600px) {
    padding: 2rem 1rem;
  }
`;

const FormTitle = styled.h2`
  font-family: '<PERSON><PERSON> Neue', sans-serif;
  font-size: 2rem;
  color: #23281d;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-size: 1.1rem;
  color: #23281d;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0.2rem;
  background: #f8f6ea;
`;

const Select = styled.select`
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  background: #f8f6ea;
`;

const SubmitButton = styled.button`
  width: 100%;
  background: #f16925;
  color: #fff;
  font-size: 1.2rem;
  font-family: 'Bebas Neue', sans-serif;
  font-weight: bold;
  border: none;
  border-radius: 6px;
  padding: 1rem 0;
  margin-top: 1rem;
  cursor: pointer;
  letter-spacing: 1px;
  transition: background 0.2s;
  &:hover {
    background: #d35400;
  }
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  @media (max-width: 700px) {
    flex-direction: column;
    gap: 0;
  }
`;

const HalfGroup = styled(FormGroup)`
  flex: 1;
`;

const CallToAction = () => {
  const { language } = useLanguageStore();

  const categories = [
    { value: 'restaurant', label: language === 'es' ? 'Restaurante' : 'Restaurant' },
    { value: 'beauty', label: language === 'es' ? 'Belleza' : 'Beauty' },
    { value: 'retail', label: language === 'es' ? 'Tienda' : 'Retail' },
  ];

  return (
    <FormSection id="contact">
      <FormContainer>
        <FormTitle>
          {language === 'es' ? 'Traiga su negocio al Mercado Food Hall & Shops' : ' Bring Your Business to Mercado Food Hall & Shops'}
        </FormTitle>
        <form>
          <FormRow>
            <HalfGroup>
              <Label htmlFor="firstName">{language === 'es' ? 'Nombre' : 'First name'}</Label>
              <Input id="firstName" type="text" name="firstName" />
            </HalfGroup>
            <HalfGroup>
              <Label htmlFor="lastName">{language === 'es' ? 'Apellido*' : 'Last name*'}</Label>
              <Input id="lastName" type="text" name="lastName" required />
            </HalfGroup>
          </FormRow>
          <FormRow>
            <HalfGroup>
              <Label htmlFor="phone">{language === 'es' ? 'Teléfono' : 'Phone number'}</Label>
              <Input id="phone" type="tel" name="phone" />
            </HalfGroup>
            <HalfGroup>
              <Label htmlFor="email">{language === 'es' ? 'Correo electrónico*' : 'Email address*'}</Label>
              <Input id="email" type="email" name="email" required />
            </HalfGroup>
          </FormRow>
          <FormGroup>
            <Label htmlFor="businessName">{language === 'es' ? 'Nombre del negocio' : 'Business name'}</Label>
            <Input id="businessName" type="text" name="businessName" />
          </FormGroup>
          <FormRow>
            <HalfGroup>
              <Label htmlFor="operating">{language === 'es' ? '¿El negocio ya está operando?' : 'Business already operating'}</Label>
              <Select id="operating" name="operating">
                <option value="">{language === 'es' ? 'Por favor selecciona' : 'Please select'}</option>
                <option value="yes">{language === 'es' ? 'Sí' : 'Yes'}</option>
                <option value="no">{language === 'es' ? 'No' : 'No'}</option>
              </Select>
            </HalfGroup>
            <HalfGroup>
              <Label htmlFor="products">{language === 'es' ? '¿Qué productos o servicios vendes?' : 'Products or services you are selling'}</Label>
              <Input id="products" type="text" name="products" />
            </HalfGroup>
          </FormRow>
          <FormGroup>
            <Label htmlFor="category">{language === 'es' ? 'Categoría del negocio' : 'Business category'}</Label>
            <Select id="category" name="category" required>
              <option value="">{language === 'es' ? 'Por favor selecciona' : 'Please select'}</option>
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </Select>
          </FormGroup>
          <SubmitButton type="submit">
            {language === 'es' ? 'Enviar' : 'Submit'}
          </SubmitButton>
        </form>
      </FormContainer>
    </FormSection>
  );
};

export default CallToAction; 