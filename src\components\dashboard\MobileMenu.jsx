import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Edit2, Package, Bot, ArrowLeft, Home, Gift, History, Menu, X, Globe } from 'lucide-react';
import { useLanguageStore } from '../../store/useLanguageStore';

const MobileMenuButton = styled(motion.button)`
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background: #D8DF20;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  color: #000;
  display: none;

  @media (max-width: 1024px) {
    display: block;
    padding: 0.4rem;
    top: 0.8rem;
    left: 0.8rem;
  }

  @media (max-width: 480px) {
    padding: 0.3rem;
    top: 0.6rem;
    left: 0.6rem;
  }
`;

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;

  @media (max-width: 1024px) {
    display: block;
  }
`;

const MobileMenuContainer = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 80%;
  max-width: 300px;
  background: #000;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  display: none;

  @media (max-width: 1024px) {
    display: flex;
    width: 85%;
    padding: 1.2rem;
  }

  @media (max-width: 480px) {
    width: 90%;
    padding: 1rem;
  }
`;

const Logo = styled.img`
  width: 50px;
  height: 50px;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
    margin-bottom: 1.5rem;
  }
`;

const NavList = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  margin-top: 1rem;

  @media (max-width: 480px) {
    gap: 0.5rem;
  }
`;

const NavItem = styled(Link)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  color: ${props => props.$active ? '#D8DF20' : '#fff'};
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  background: ${props => props.$active ? 'rgba(216, 223, 32, 0.1)' : 'transparent'};
  border: 1px solid ${props => props.$active ? '#D8DF20' : 'transparent'};

  &:hover {
    background: rgba(216, 223, 32, 0.1);
    color: #D8DF20;
    transform: translateX(5px);
  }

  svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: ${props => props.$active ? '#D8DF20' : '#fff'};
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
    gap: 0.8rem;

    svg {
      width: 18px;
      height: 18px;
    }
  }
`;

const LanguageButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: none;
  border: 2px solid #D8DF20;
  color: #D8DF20;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  cursor: pointer;
  margin-top: 1rem;

  &:hover {
    background: #D8DF20;
    color: #000;
    transform: translateY(-2px);
  }

  svg {
    width: 20px;
    height: 20px;
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
    gap: 0.4rem;

    svg {
      width: 18px;
      height: 18px;
    }
  }
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #D8DF20;
  color: #000;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  transition: all 0.3s ease;
  width: 100%;
  border: none;

  &:hover {
    background: #e8ef30;
    transform: translateY(-2px);
  }

  svg {
    width: 20px;
    height: 20px;
    color: #000;
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
    gap: 0.4rem;

    svg {
      width: 18px;
      height: 18px;
    }
  }
`;

const MobileMenu = () => {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const { language, toggleLanguage } = useLanguageStore();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      <MobileMenuButton
        onClick={toggleMenu}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </MobileMenuButton>

      <AnimatePresence>
        {isOpen && (
          <>
            <Overlay
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={toggleMenu}
            />
            <MobileMenuContainer
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <Logo src="/logo-eme.svg" alt="EME Logo" />

              <NavList>
                <NavItem
                  to="/dashboard"
                  $active={location.pathname === '/dashboard'}
                >
                  <Home size={20} />
                  <span>{language === 'es' ? 'Inicio' : 'Home'}</span>
                </NavItem>

                <NavItem
                  to="/dashboard/edit"
                  $active={location.pathname === '/dashboard/edit'}
                >
                  <Edit2 size={20} />
                  <span>{language === 'es' ? 'Editar Stand' : 'Edit Stand'}</span>
                </NavItem>

                <NavItem
                  to="/dashboard/products"
                  $active={location.pathname === '/dashboard/products'}
                >
                  <Package size={20} />
                  <span>{language === 'es' ? 'Productos' : 'Products'}</span>
                </NavItem>

                <NavItem
                  to="/dashboard/ai"
                  $active={location.pathname === '/dashboard/ai'}
                >
                  <Bot size={20} />
                  <span>{language === 'es' ? 'IA' : 'AI'}</span>
                </NavItem>

                <NavItem
                  to="/dashboard/points"
                  $active={location.pathname === '/dashboard/points'}
                >
                  <Gift size={20} />
                  <span>{language === 'es' ? 'Puntos' : 'Points'}</span>
                </NavItem>

                <NavItem
                  to="/dashboard/points-history"
                  $active={location.pathname === '/dashboard/points-history'}
                >
                  <History size={20} />
                  <span>{language === 'es' ? 'Historial' : 'History'}</span>
                </NavItem>
              </NavList>

              <LanguageButton
                onClick={toggleLanguage}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Globe size={20} />
                <span>{language === 'es' ? 'English' : 'Español'}</span>
              </LanguageButton>

              <BackButton to="/">
                <ArrowLeft size={20} />
                <span>{language === 'es' ? 'Volver al Inicio' : 'Back to Home'}</span>
              </BackButton>
            </MobileMenuContainer>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileMenu; 