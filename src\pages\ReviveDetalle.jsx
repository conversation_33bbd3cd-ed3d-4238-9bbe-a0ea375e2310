import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Play, Info, Award, User } from 'lucide-react';
import { useAuthStore } from '../store/useAuthStore';

const PageWrapper = styled.div`
  background-color: #141414;
  min-height: 100vh;
  color: white;
`;

const HeroSection = styled.div`
  position: relative;
  height: 70vh;
  background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.8)),
              url('https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
  padding: 0 4rem 4rem;
`;

const ContentWrapper = styled.div`
  max-width: 800px;
  z-index: 1;
`;

const Title = styled.h1`
  font-size: 3.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
`;

const Year = styled.span`
  font-size: 1.2rem;
  color: #D8DF20;
  margin-bottom: 1rem;
  display: block;
`;

const Description = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  line-height: 1.5;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;

  &.play {
    background-color: white;
    color: black;

    &:hover {
      background-color: rgba(255,255,255,0.8);
    }
  }

  &.info {
    background-color: rgba(109, 109, 110, 0.7);
    color: white;

    &:hover {
      background-color: rgba(109, 109, 110, 0.9);
    }
  }

  &.certificate {
    background-color: #D8DF20;
    color: black;

    &:hover {
      background-color: #c1c71c;
    }
  }
`;

const SpeakerSection = styled.div`
  padding: 2rem 4rem;
  display: flex;
  gap: 2rem;
  align-items: center;
`;

const SpeakerImage = styled.img`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
`;

const SpeakerInfo = styled.div`
  flex: 1;
`;

const SpeakerName = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
`;

const SpeakerBio = styled.p`
  font-size: 1.1rem;
  color: #ccc;
  line-height: 1.5;
`;

const LoginOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  padding: 2rem;
  text-align: center;
`;

const LoginMessage = styled.p`
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: white;
`;

const LoginButton = styled(Button)`
  background-color: #D8DF20;
  color: black;
  padding: 1rem 2rem;
  font-size: 1.2rem;

  &:hover {
    background-color: #c1c71c;
  }
`;

// Datos mockeados (en un caso real, esto vendría de una API)
const mockData = {
  id: 1,
  title: "EME Summit 2024",
  description: "La conferencia más importante del año sobre desarrollo personal y espiritual",
  year: "2024",
  speaker: {
    name: "Dr. Alejandro Martínez",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    bio: "Experto en desarrollo personal y espiritual con más de 20 años de experiencia"
  },
  fullDescription: "El EME Summit 2024 reúne a los principales expertos en desarrollo personal y espiritual para compartir las últimas tendencias y técnicas en el campo. Una experiencia transformadora que te llevará a un nuevo nivel de consciencia.",
  videoId: "abc123"
};

const ReviveDetalle = () => {
  const navigate = useNavigate();
  const user = useAuthStore((state) => state.user);

  const handlePlay = () => {
    navigate(`/reproducir/${mockData.videoId}`);
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleGenerateCertificate = () => {
    // Aquí iría la lógica para generar el certificado
    alert('Generando certificado...');
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <HeroSection>
          <ContentWrapper>
            <Title>{mockData.title}</Title>
            <Year>{mockData.year}</Year>
            <Description>{mockData.fullDescription}</Description>
            <ButtonGroup>
              <Button className="play" onClick={handlePlay}>
                <Play size={20} />
                Reproducir
              </Button>
              <Button className="info">
                <Info size={20} />
                Más información
              </Button>
              {user && (
                <Button className="certificate" onClick={handleGenerateCertificate}>
                  <Award size={20} />
                  Generar Certificado
                </Button>
              )}
            </ButtonGroup>
          </ContentWrapper>
        </HeroSection>

        <SpeakerSection>
          <SpeakerImage src={mockData.speaker.image} alt={mockData.speaker.name} loading="lazy" />
          <SpeakerInfo>
            <SpeakerName>{mockData.speaker.name}</SpeakerName>
            <SpeakerBio>{mockData.speaker.bio}</SpeakerBio>
          </SpeakerInfo>
        </SpeakerSection>

        {!user && (
          <LoginOverlay>
            <LoginMessage>
              Inicia sesión para ver el contenido completo y generar tu certificado
            </LoginMessage>
            <LoginButton onClick={handleLogin}>
              <User size={20} />
              Iniciar Sesión
            </LoginButton>
          </LoginOverlay>
        )}
      </PageWrapper>
    </>
  );
};

export default ReviveDetalle; 