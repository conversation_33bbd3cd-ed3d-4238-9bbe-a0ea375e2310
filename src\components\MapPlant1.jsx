import React from 'react';
import styled from 'styled-components';
import { Navbar } from './Navbar';

const MapContainer = styled.div`
  width: 100%;
  height: 650px;
  margin-bottom: 2rem;
`;

const TableContainer = styled.div`
  width: 100%;
  padding: 1rem;
  margin-top: 2rem;
`;

const MapPlant1 = () => {
  return (
    <div>
      <Navbar />
      <MapContainer>
        <iframe 
          src="https://app.mappedin.com/map/68474467513713000bd1ef16?embedded=true"
          title="Mappedin Map"
          name="Mappedin Map"
          allow="clipboard-write 'self' https://app.mappedin.com; web-share 'self' https://app.mappedin.com"
          scrolling="no"
          width="100%"
          height="650"
          frameBorder="0"
          style={{ border: 0 }}
        />
      </MapContainer>
      <TableContainer>
        {/* Aquí irá la tabla en el futuro */}
      </TableContainer>
    </div>
  );
};

export default MapPlant1; 