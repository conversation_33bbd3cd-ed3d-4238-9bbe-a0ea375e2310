import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';

const PageWrapper = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: white;
  position: relative;
  overflow: hidden;
`;

const WaveContainer = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
`;

const Wave = styled(motion.div)`
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(216, 223, 32, 0.1) 0%,
    rgba(76, 175, 80, 0.1) 50%,
    rgba(216, 223, 32, 0.1) 100%
  );
  transform-origin: 50% 50%;
  border-radius: 45%;
`;

const WelcomeContainer = styled.div`
  text-align: center;
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 1;
`;

const Title = styled(motion.h1)`
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #D8DF20, #4CAF50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(216, 223, 32, 0.3);
`;

const Subtitle = styled(motion.p)`
  font-size: 1.5rem;
  margin-bottom: 3rem;
  color: #ffffff;
  opacity: 0.8;
`;

const InputContainer = styled(motion.div)`
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const Input = styled.input`
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.2rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  outline: none;
  transition: all 0.3s ease;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    border-color: #D8DF20;
    background: rgba(255, 255, 255, 0.15);
  }
`;

const Button = styled(motion.button)`
  margin-top: 1rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(45deg, #D8DF20, #4CAF50);
  color: black;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(216, 223, 32, 0.3);
  }
`;

const WelcomeMessage = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  overflow: hidden;
`;

const MessageContent = styled(motion.div)`
  text-align: center;
  padding: 2rem;
  position: relative;
`;

const AnimatedWave = styled(motion.div)`
  position: absolute;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(216, 223, 32, 0.2) 0%,
    rgba(76, 175, 80, 0.2) 50%,
    rgba(216, 223, 32, 0.2) 100%
  );
  transform-origin: 50% 50%;
  border-radius: 45%;
`;

const Welcome = () => {
  const [name, setName] = useState('');
  const [showWelcome, setShowWelcome] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      setShowWelcome(true);
      setTimeout(() => {
        setShowWelcome(false);
        setName('');
      }, 5000);
    }
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <WaveContainer>
          <Wave
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </WaveContainer>

        <WelcomeContainer>
          <Title
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            EME Fest
          </Title>
          <Subtitle
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            ¡Bienvenido a la experiencia más innovadora!
          </Subtitle>

          <AnimatePresence>
            {!showWelcome && (
              <InputContainer
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                <form onSubmit={handleSubmit}>
                  <Input
                    type="text"
                    placeholder="Ingresa tu nombre"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                  <Button
                    type="submit"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Comenzar
                  </Button>
                </form>
              </InputContainer>
            )}
          </AnimatePresence>
        </WelcomeContainer>

        <AnimatePresence>
          {showWelcome && (
            <WelcomeMessage
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              <AnimatedWave
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
              <MessageContent>
                <Title
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.5, opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  ¡Hola, {name}!
                </Title>
                <Subtitle
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 20, opacity: 0 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                >
                  Bienvenido a EME Fest
                </Subtitle>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                  style={{
                    width: '150px',
                    height: '150px',
                    margin: '2rem auto',
                    background: 'linear-gradient(45deg, #D8DF20, #4CAF50)',
                    borderRadius: '50%',
                    filter: 'blur(30px)',
                    opacity: 0.6
                  }}
                />
              </MessageContent>
            </WelcomeMessage>
          )}
        </AnimatePresence>
      </PageWrapper>
    </>
  );
};

export default Welcome; 