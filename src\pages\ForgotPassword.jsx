import { useState } from 'react'
import { ArrowRight, Mail, ArrowLeft } from 'lucide-react'
import styled from 'styled-components'
import { useAuthStore } from '../store/useAuthStore'
import { useNavigate, Link } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import Loader from '../components/Loader'

const breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px'
}

const Container = styled.div`
  min-height: 100vh;
  background-image: url("/register.webp");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
`

const Navbar = styled.nav`
  background-color: #F16925;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  width: 100%;
`

const NavLogo = styled.img`
  height: 40px;
  object-fit: contain;

  @media (min-width: ${breakpoints.tablet}) {
    height: 50px;
  }
`

const FormContainer = styled.div`
  width: 100%;
  max-width: 400px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: ${breakpoints.mobile}) {
    margin: 1rem auto;
    padding: 1rem;
    max-width: 90%;
  }

  @media (min-width: ${breakpoints.tablet}) {
    max-width: 500px;
    padding: 2.5rem;
  }

  @media (min-width: ${breakpoints.desktop}) {
    max-width: 600px;
    margin: 4rem auto;
  }
`

const Header = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: ${breakpoints.mobile}) {
    margin-bottom: 1.5rem;
  }
`

const Title = styled.h1`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 1rem 0;

  @media (min-width: ${breakpoints.tablet}) {
    font-size: 2rem;
  }
`

const Description = styled.p`
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;

  @media (min-width: ${breakpoints.tablet}) {
    font-size: 1.1rem;
  }
`

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
`

const FormGroup = styled.div`
  position: relative;
  width: 100%;
`

const Input = styled.input`
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:focus {
    outline: none;
    border-color: #D8DF20;
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`

const SubmitButton = styled.button`
  width: 100%;
  padding: 1rem;
  background-color: #2C2C2C;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
  margin-top: 1rem;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:hover:not(:disabled) {
    background-color: #414141;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
  }
`

const BackLink = styled(Link)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
  margin-top: 1rem;
  transition: color 0.2s;

  &:hover {
    color: #333;
  }
`

const SuccessMessage = styled.div`
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  text-align: center;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`

const ErrorMessage = styled.p`
  color: red;
  text-align: center;
  margin-top: 1rem;
`

const ForgotPassword = () => {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [emailSent, setEmailSent] = useState(false)
  const navigate = useNavigate()
  const requestPasswordReset = useAuthStore((state) => state.requestPasswordReset)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const { error: resetError } = await requestPasswordReset(email)
      
      if (resetError) {
        setError(resetError)
        return
      }

      setEmailSent(true)
      toast.success('¡Correo de recuperación enviado!')
      
      // Redirect to login after 5 seconds
      setTimeout(() => {
        navigate('/login')
      }, 5000)

    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <Loader />
  }

  return (
    <Container>
      <Navbar>
        <a href="/">
          <NavLogo 
            src="/logo.webp"
            alt="Logo" 
          />
        </a>
      </Navbar>

      <FormContainer>
        <Header>
          <Title>¿Olvidaste tu contraseña?</Title>
          <Description>
            Ingresa tu correo electrónico y te enviaremos un enlace para restablecer tu contraseña.
          </Description>
        </Header>

        {emailSent ? (
          <SuccessMessage>
            <Mail size={20} />
            Hemos enviado un enlace de recuperación a tu correo electrónico. 
            Revisa tu bandeja de entrada (y spam).
          </SuccessMessage>
        ) : (
          <Form onSubmit={handleSubmit}>
            <FormGroup>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Correo electrónico"
                required
                disabled={loading}
              />
            </FormGroup>

            {error && <ErrorMessage>{error}</ErrorMessage>}

            <SubmitButton type="submit" disabled={loading || !email}>
              {loading ? 'Enviando...' : 'Enviar enlace de recuperación'}
              {!loading && <ArrowRight size={20} />}
            </SubmitButton>
          </Form>
        )}

        <BackLink to="/login">
          <ArrowLeft size={16} />
          Volver al inicio de sesión
        </BackLink>
      </FormContainer>
    </Container>
  )
}

export default ForgotPassword 