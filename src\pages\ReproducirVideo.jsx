import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { ArrowLeft } from 'lucide-react';

const PageWrapper = styled.div`
  background-color: #141414;
  min-height: 100vh;
  color: white;
  position: relative;
`;

const VideoContainer = styled.div`
  width: 100%;
  height: 100vh;
  position: relative;
`;

const BackButton = styled.button`
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
`;

const YouTubeIframe = styled.iframe`
  width: 100%;
  height: 100%;
  border: none;
`;

function ReproducirVideo() {
  const { id } = useParams();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(`/revive-detalle/${id}`);
  };

  // ID del video de YouTube (en un caso real, esto vendría de una API)
  const videoId = "3X30vGbLol8";

  return (
    <PageWrapper>
      <BackButton onClick={handleBack}>
        <ArrowLeft size={20} />
        Regresar
      </BackButton>
      <VideoContainer>
        <YouTubeIframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      </VideoContainer>
    </PageWrapper>
  );
}

export default ReproducirVideo; 