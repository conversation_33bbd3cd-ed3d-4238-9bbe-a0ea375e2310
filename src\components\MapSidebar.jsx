// src/components/MapSidebar.jsx
import React from 'react';
import styled from 'styled-components';
import { Search } from 'lucide-react';
import { FaMapMarkerAlt } from 'react-icons/fa';

const Sidebar = styled.div`
  width: 300px;
  background: #000;
  border-right: 1px solid #333;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow-y: scroll;

  @media (max-width: 768px) {
    width: 100%;
    height: 50vh;
    border-right: none;
    border-top: 1px solid #333;
  }
`;

const SearchBox = styled.div`
  display: flex;
  align-items: center;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const SearchInput = styled.input`
  flex: 1;
  border: none;
  font-size: 1rem;
  outline: none;
  margin-left: 0.5rem;
  background: transparent;
  color: #fff;

  &::placeholder {
    color: #666;
  }
`;

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
`;

const CategoryCard = styled.div`
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #fff;

  &:hover {
    transform: translateY(-2px);
    border-color: #D8DF20;
  }

  ${({ $active }) => $active && `
    background: #D8DF20;
    border-color: #D8DF20;
    color: #000;
  `}
`;

const CategoryIcon = styled.div`
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ $active }) => ($active ? '#000' : '#D8DF20')};
`;

const CategoryName = styled.span`
  font-size: 0.9rem;
  text-align: center;
`;

const MarkerCount = styled.span`
  font-size: 0.8rem;
  color: ${({ $active }) => ($active ? '#000' : '#666')};
`;

export const MapSidebar = ({ search, setSearch, selectedCategory, setSelectedCategory }) => {
  const categories = [
    { name: 'Todos', icon: <FaMapMarkerAlt size={24} /> },
    { name: 'Stands', icon: <FaMapMarkerAlt size={24} /> },
    { name: 'Baños', icon: <img src="/icons/toilet.webp" alt="Baños" loading="lazy" style={{ width: '32px', height: 'auto' }} /> },
    { name: 'Comida', icon: <FaMapMarkerAlt size={24} /> },
  ];

  const markerList = [
    { id: 1, name: 'Stands', category: 'Stands' },
    { id: 2, name: 'Baños Zona A', category: 'Baños', icon: '/icons/toilet.webp' },
    { id: 3, name: 'Zona Foodtrucks', category: 'Comida' },
  ];

  const filteredCategories = categories.filter(cat => 
    cat.name.toLowerCase().includes(search.toLowerCase())
  );

  const getMarkerCount = (category) => {
    if (category === 'Todos') return markerList.length;
    return markerList.filter(m => m.category === category).length;
  };

  return (
    <Sidebar>
      <SearchBox>
        <Search size={18} color="#D8DF20" />
        <SearchInput
          type="text"
          placeholder="Buscar categoría..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </SearchBox>

      <CategoriesGrid>
        {filteredCategories.map((cat) => (
          <CategoryCard
            key={cat.name}
            $active={selectedCategory === cat.name}
            onClick={() => setSelectedCategory(cat.name)}
          >
            <CategoryIcon $active={selectedCategory === cat.name}>
              {cat.icon}
            </CategoryIcon>
            <CategoryName>{cat.name}</CategoryName>
            <MarkerCount $active={selectedCategory === cat.name}>
              {getMarkerCount(cat.name)} marcadores
            </MarkerCount>
          </CategoryCard>
        ))}
      </CategoriesGrid>
    </Sidebar>
  );
};
