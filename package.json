{"name": "Food Hall", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "import-stands": "node scripts/importStandsFromExcel.cjs", "import-tenants": "node scripts/importTenantListings.cjs", "img:webp": "node scripts/convertToWebp.cjs"}, "dependencies": {"@supabase/supabase-js": "^2.49.3", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "framer-motion": "^12.6.2", "i18next": "^25.1.3", "lucide-react": "^0.484.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-image-marker": "^1.2.0", "react-router-dom": "^7.4.0", "react-vertical-timeline-component": "^3.5.3", "styled-components": "^6.1.16", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-react": "^0.4.9", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "sharp": "^0.33.3", "terser": "^5.43.1", "vite": "^6.2.0"}}