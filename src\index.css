:root {
  /* Optimized font stack with fallbacks */
  font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* Font display optimization */
  font-display: swap;
}

/* Bebas Neue font class for headings */
.bebas-neue {
  font-family: "Bebas Neue", "Arial Black", "Helvetica Bold", sans-serif;
  font-display: swap;
}

/* Global font optimization - override inline styles */
[style*="Bebas Neue"] {
  font-family: "Bebas Neue", "Arial Black", "Helvetica Bold", sans-serif !important;
  font-display: swap;
}

/* Performance optimization for font loading */
@font-face {
  font-family: 'Open Sans';
  font-display: swap;
  src: local('Open Sans'), local('OpenSans');
}

@font-face {
  font-family: 'Bebas Neue';
  font-display: swap;
  src: local('Bebas Neue'), local('BebasNeue');
}
