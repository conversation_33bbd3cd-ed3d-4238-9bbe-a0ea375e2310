import { useState, useEffect } from 'react'
import { Eye, EyeOff, ArrowRight, CheckCircle } from 'lucide-react'
import styled from 'styled-components'
import { useAuthStore } from '../store/useAuthStore'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import Loader from '../components/Loader'

const breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px'
}

const Container = styled.div`
  min-height: 100vh;
  background-image: url("/register.webp");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
`

const Navbar = styled.nav`
  background-color: #F16925;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  width: 100%;
`

const NavLogo = styled.img`
  height: 40px;
  object-fit: contain;

  @media (min-width: ${breakpoints.tablet}) {
    height: 50px;
  }
`

const FormContainer = styled.div`
  width: 100%;
  max-width: 400px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: ${breakpoints.mobile}) {
    margin: 1rem auto;
    padding: 1rem;
    max-width: 90%;
  }

  @media (min-width: ${breakpoints.tablet}) {
    max-width: 500px;
    padding: 2.5rem;
  }

  @media (min-width: ${breakpoints.desktop}) {
    max-width: 600px;
    margin: 4rem auto;
  }
`

const Header = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: ${breakpoints.mobile}) {
    margin-bottom: 1.5rem;
  }
`

const Title = styled.h1`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 1rem 0;

  @media (min-width: ${breakpoints.tablet}) {
    font-size: 2rem;
  }
`

const Description = styled.p`
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;

  @media (min-width: ${breakpoints.tablet}) {
    font-size: 1.1rem;
  }
`

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
`

const FormGroup = styled.div`
  position: relative;
  width: 100%;
`

const Input = styled.input`
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:focus {
    outline: none;
    border-color: #D8DF20;
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
`

const PasswordToggle = styled.button`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 8px;
  display: flex;
  align-items: center;
  
  &:hover {
    color: #333;
  }
`

const SubmitButton = styled.button`
  width: 100%;
  padding: 1rem;
  background-color: #2C2C2C;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
  margin-top: 1rem;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:hover:not(:disabled) {
    background-color: #414141;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
  }
`

const PasswordStrength = styled.div`
  font-size: 0.9rem;
  margin-top: 0.5rem;
  
  ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
    color: #666;
  }
  
  li {
    margin: 0.25rem 0;
    
    &.valid {
      color: #22c55e;
    }
  }
`

const SuccessMessage = styled.div`
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  text-align: center;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`

const ErrorMessage = styled.p`
  color: red;
  text-align: center;
  margin-top: 1rem;
`

const ResetPassword = () => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  const [isValidSession, setIsValidSession] = useState(false)
  const navigate = useNavigate()
  const resetPassword = useAuthStore((state) => state.resetPassword)

  useEffect(() => {
    // Check if there's a valid session for password reset
    const checkSession = async () => {
      const hashParams = new URLSearchParams(window.location.hash.substring(1))
      const accessToken = hashParams.get('access_token')
      const type = hashParams.get('type')
      
      if (type === 'recovery' && accessToken) {
        setIsValidSession(true)
      } else {
        setError('Enlace de recuperación inválido o expirado. Solicita uno nuevo.')
      }
    }
    
    checkSession()
  }, [])

  const validatePassword = (pwd) => {
    const requirements = {
      length: pwd.length >= 8,
      uppercase: /[A-Z]/.test(pwd),
      lowercase: /[a-z]/.test(pwd),
      number: /\d/.test(pwd),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(pwd)
    }
    
    return requirements
  }

  const passwordRequirements = validatePassword(password)
  const isPasswordValid = Object.values(passwordRequirements).every(req => req)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden')
      setLoading(false)
      return
    }

    // Validate password strength
    if (!isPasswordValid) {
      setError('La contraseña no cumple con los requisitos de seguridad')
      setLoading(false)
      return
    }

    try {
      const { error: resetError } = await resetPassword(password)
      
      if (resetError) {
        setError(resetError)
        return
      }

      setSuccess(true)
      toast.success('¡Contraseña actualizada exitosamente!')
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login')
      }, 3000)

    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <Loader />
  }

  return (
    <Container>
      <Navbar>
        <a href="/">
          <NavLogo 
            src="/logo.webp"
            alt="Logo" 
          />
        </a>
      </Navbar>

      <FormContainer>
        <Header>
          <Title>Restablecer Contraseña</Title>
          <Description>
            Ingresa tu nueva contraseña para completar la recuperación de tu cuenta.
          </Description>
        </Header>

        {!isValidSession && !success ? (
          <ErrorMessage>{error}</ErrorMessage>
        ) : success ? (
          <SuccessMessage>
            <CheckCircle size={20} />
            ¡Contraseña actualizada exitosamente! Redirigiendo al inicio de sesión...
          </SuccessMessage>
        ) : (
          <Form onSubmit={handleSubmit}>
            <FormGroup>
              <Input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Nueva contraseña"
                required
                disabled={loading}
              />
              <PasswordToggle 
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </PasswordToggle>
            </FormGroup>

            {password && (
              <PasswordStrength>
                <strong>Requisitos de contraseña:</strong>
                <ul>
                  <li className={passwordRequirements.length ? 'valid' : ''}>
                    Al menos 8 caracteres
                  </li>
                  <li className={passwordRequirements.uppercase ? 'valid' : ''}>
                    Una letra mayúscula
                  </li>
                  <li className={passwordRequirements.lowercase ? 'valid' : ''}>
                    Una letra minúscula
                  </li>
                  <li className={passwordRequirements.number ? 'valid' : ''}>
                    Un número
                  </li>
                  <li className={passwordRequirements.special ? 'valid' : ''}>
                    Un carácter especial (!@#$%^&*)
                  </li>
                </ul>
              </PasswordStrength>
            )}

            <FormGroup>
              <Input
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirmar nueva contraseña"
                required
                disabled={loading}
              />
              <PasswordToggle 
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={loading}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </PasswordToggle>
            </FormGroup>

            {error && <ErrorMessage>{error}</ErrorMessage>}

            <SubmitButton 
              type="submit" 
              disabled={loading || !password || !confirmPassword || !isPasswordValid}
            >
              {loading ? 'Actualizando...' : 'Actualizar contraseña'}
              {!loading && <ArrowRight size={20} />}
            </SubmitButton>
          </Form>
        )}
      </FormContainer>
    </Container>
  )
}

export default ResetPassword 