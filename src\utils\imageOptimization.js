// Image optimization utility for better performance

// Generate responsive image sources with WebP fallbacks
export const generateResponsiveImageSrc = (imagePath, sizes = ['480', '768', '1024', '1920']) => {
  const baseUrl = imagePath.replace(/\.(jpg|jpeg|png|webp)$/i, '');
  const extension = imagePath.match(/\.(jpg|jpeg|png|webp)$/i)?.[1] || 'webp';
  
  // Generate WebP sources for modern browsers
  const webpSources = sizes.map(size => ({
    srcset: `${baseUrl}-${size}w.webp`,
    media: `(max-width: ${size}px)`,
    type: 'image/webp'
  }));
  
  // Generate fallback sources for older browsers
  const fallbackSources = sizes.map(size => ({
    srcset: `${baseUrl}-${size}w.${extension}`,
    media: `(max-width: ${size}px)`,
    type: `image/${extension}`
  }));
  
  return {
    webp: webpSources,
    fallback: fallbackSources,
    defaultSrc: `${baseUrl}-1024w.${extension}` // Default fallback
  };
};

// Optimized image component props
export const getOptimizedImageProps = (src, alt, loading = 'lazy') => {
  return {
    src,
    alt,
    loading,
    decoding: 'async',
    // Add fetchpriority for critical images
    fetchpriority: loading === 'eager' ? 'high' : 'auto',
    // Add sizes attribute for responsive images
    sizes: '(max-width: 480px) 480px, (max-width: 768px) 768px, (max-width: 1024px) 1024px, 1920px'
  };
};

// Critical images that should be preloaded
export const CRITICAL_IMAGES = [
  '/logo-2.webp',
  '/logo.webp',
  '/hero-main.webp'
];

// Large images that need optimization
export const LARGE_IMAGES = {
  '/about1-4.webp': {
    optimized: '/about1-4-optimized.webp',
    fallback: '/about1-4-optimized.webp',
    sizes: '(max-width: 420px) 200px, 420px'
  },
  '/mark.webp': {
    optimized: '/mark-optimized.webp',
    fallback: '/mark-optimized.webp',
    sizes: '100vw'
  },
  '/about1-1.webp': {
    optimized: '/about1-1-optimized.webp',
    fallback: '/about1-1-optimized.webp',
    sizes: '(max-width: 420px) 200px, 420px'
  },
  '/about1-2.webp': {
    optimized: '/about1-2-optimized.webp',
    fallback: '/about1-2-optimized.webp',
    sizes: '(max-width: 420px) 200px, 420px'
  },
  '/about1-3.webp': {
    optimized: '/about1-3-optimized.webp',
    fallback: '/about1-3-optimized.webp',
    sizes: '(max-width: 420px) 200px, 420px'
  },
  '/register.webp': {
    optimized: '/register-optimized.webp',
    fallback: '/register-optimized.webp',
    sizes: '100vw'
  }
};

// Get optimized image URL
export const getOptimizedImageUrl = (originalSrc) => {
  const optimizedImage = LARGE_IMAGES[originalSrc];
  if (optimizedImage) {
    return optimizedImage.optimized;
  }
  return originalSrc;
};

// Check if browser supports WebP
export const supportsWebP = () => {
  if (typeof window === 'undefined') return false;
  
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};

// Preload critical images
export const preloadCriticalImages = () => {
  if (typeof window === 'undefined') return;
  
  CRITICAL_IMAGES.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
}; 