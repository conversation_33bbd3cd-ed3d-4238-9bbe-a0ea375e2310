import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useUserStore } from '../../store/useUserStore';
import { useAuthStore } from '../../store/useAuthStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #D8DF20;
  margin: 0;
  font-size: 2rem;
`;

const Description = styled.p`
  color: #888;
  margin: 0.5rem 0 0 0;
  font-size: 1.1rem;
`;

const Form = styled.form`
  background: #222;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  color: #D8DF20;
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 1rem;
  background: #333;
  border: 1px solid #444;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  min-height: 300px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.2);
  }
`;

const Button = styled.button`
  background: #D8DF20;
  color: #000;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #c4cb1c;
    transform: translateY(-1px);
  }

  &:disabled {
    background: #444;
    cursor: not-allowed;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #D8DF20;
  font-size: 1.2rem;
`;

const AIStand = () => {
  const { userData } = useUserStore();
  const { user } = useAuthStore();
  const { language } = useLanguageStore();
  const [loading, setLoading] = useState(false);
  const [knowledgeBase, setKnowledgeBase] = useState('');
  const [currentStandId, setCurrentStandId] = useState(null);

  useEffect(() => {
    fetchStandId();
  }, [user?.id]);

  const fetchStandId = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      console.log('Consultando datos del usuario:', user.id);
      
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*, stands(*)')
        .eq('id', user.id)
        .single();

      console.log('Datos del usuario recibidos:', userData);

      if (userError) throw userError;

      if (userData?.stands && userData.stands.length > 0) {
        const standId = userData.stands[0].id;
        console.log('StandId encontrado:', standId);
        setCurrentStandId(standId);
        fetchKnowledgeBase(standId);
      } else {
        console.log('No se encontró stand asociado al usuario');
      }
    } catch (error) {
      console.error('Error fetching stand ID:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchKnowledgeBase = async (standId) => {
    if (!standId) return;

    try {
      setLoading(true);
      console.log('Consultando ai_stands para standId:', standId);
      
      const { data: standData, error: standError } = await supabase
        .from('stands')
        .select('ai_stands')
        .eq('id', standId)
        .single();

      console.log('Datos del stand recibidos:', standData);
      console.log('Error si existe:', standError);

      if (standError) throw standError;

      console.log('Valor de ai_stands:', standData?.ai_stands);
      setKnowledgeBase(standData?.ai_stands || '');
      
      console.log('Estado knowledgeBase actualizado:', standData?.ai_stands || '');
    } catch (error) {
      console.error('Error fetching knowledge base:', error);
      setKnowledgeBase('');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!currentStandId) return;

    try {
      setLoading(true);
      console.log('Guardando knowledgeBase:', knowledgeBase);

      const { error } = await supabase
        .from('stands')
        .update({ ai_stands: knowledgeBase })
        .eq('id', currentStandId);

      if (error) throw error;
      alert(language === 'es' ? 'Base de conocimiento guardada exitosamente' : 'Knowledge base saved successfully');
    } catch (error) {
      console.error('Error saving knowledge base:', error);
      alert(language === 'es' ? 'Error al guardar la base de conocimiento' : 'Error saving knowledge base');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingContainer>{language === 'es' ? 'Cargando...' : 'Loading...'}</LoadingContainer>;
  }

  return (
    <Container>
      <Header>
        <Title>{language === 'es' ? 'Base de Conocimiento del Asistente Virtual' : 'Virtual Assistant Knowledge Base'}</Title>
        <Description>
          {language === 'es' 
            ? 'Define la información y el contexto que tu asistente virtual debe conocer sobre tu stand, productos, servicios y políticas.'
            : 'Define the information and context that your virtual assistant should know about your stand, products, services and policies.'
          }
        </Description>
      </Header>

      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>{language === 'es' ? 'Base de Conocimiento' : 'Knowledge Base'}</Label>
          <TextArea
            value={knowledgeBase}
            onChange={(e) => setKnowledgeBase(e.target.value)}
            placeholder={language === 'es' 
              ? 'Describe la información principal que el asistente virtual debe conocer sobre tu stand, productos, servicios y políticas...'
              : 'Describe the main information that the virtual assistant should know about your stand, products, services and policies...'
            }
            required
          />
        </FormGroup>

        <Button type="submit" disabled={loading}>
          {loading 
            ? (language === 'es' ? 'Guardando...' : 'Saving...') 
            : (language === 'es' ? 'Guardar Base de Conocimiento' : 'Save Knowledge Base')
          }
        </Button>
      </Form>
    </Container>
  );
};

export default AIStand; 