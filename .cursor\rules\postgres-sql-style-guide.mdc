---
description: 
globs: 
alwaysApply: true
---
# Postgres SQL Style Guide

You're a Supabase Postgres expert. Follow these **SQL style guidelines** to write clean, readable, and maintainable PostgreSQL code:

## General Formatting

### Keywords and Identifiers

```sql
-- Use UPPERCASE for SQL keywords
SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP

-- Use lowercase for identifiers (tables, columns, functions)
users, email_address, created_at, get_user_profile()

-- Use snake_case for multi-word identifiers
user_profiles, email_verification_tokens, updated_at
```

### Indentation and Spacing

```sql
-- Use 2 or 4 spaces for indentation (be consistent)
SELECT 
    id,
    email,
    created_at
FROM users
WHERE status = 'active'
    AND verified = true;

-- Line breaks for readability
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Table Design

### Naming Conventions

```sql
-- Table names: plural, lowercase, snake_case
users, user_profiles, email_templates, order_items

-- Column names: descriptive, lowercase, snake_case
id, user_id, email_address, created_at, is_verified

-- Primary keys: 'id' (simple and consistent)
id UUID PRIMARY KEY DEFAULT gen_random_uuid()

-- Foreign keys: '{table}_id'
user_id, profile_id, order_id
```

### Column Definitions

```sql
-- Always specify NOT NULL when appropriate
email TEXT NOT NULL,
username TEXT UNIQUE NOT NULL,

-- Use appropriate data types
id UUID,                    -- for primary keys
email TEXT,                 -- for strings
is_active BOOLEAN,          -- for true/false
price DECIMAL(10,2),        -- for money
created_at TIMESTAMP WITH TIME ZONE,  -- for timestamps

-- Add constraints with meaningful names
CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
CONSTRAINT users_username_length CHECK (char_length(username) >= 3)
```

## Query Formatting

### SELECT Statements

```sql
-- Simple queries
SELECT id, name, email
FROM users
WHERE status = 'active';

-- Complex queries with proper indentation
SELECT 
    u.id,
    u.email,
    p.full_name,
    p.avatar_url,
    COUNT(o.id) as order_count
FROM users u
    JOIN profiles p ON u.id = p.user_id
    LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at >= '2023-01-01'
    AND u.status = 'active'
GROUP BY u.id, u.email, p.full_name, p.avatar_url
HAVING COUNT(o.id) > 0
ORDER BY u.created_at DESC
LIMIT 100;
```

### JOINs

```sql
-- Explicit JOIN syntax (avoid implicit joins)
SELECT 
    u.name,
    p.title
FROM users u
    INNER JOIN posts p ON u.id = p.author_id;

-- Table aliases for readability
FROM users u
    JOIN user_profiles up ON u.id = up.user_id
    LEFT JOIN user_settings us ON u.id = us.user_id;
```

### WHERE Clauses

```sql
-- Logical grouping with parentheses
WHERE (status = 'active' OR status = 'pending')
    AND created_at >= '2023-01-01'
    AND (role = 'admin' OR role = 'moderator');

-- Use IN for multiple values
WHERE status IN ('active', 'pending', 'verified');

-- Use EXISTS for subqueries when appropriate
WHERE EXISTS (
    SELECT 1 
    FROM user_roles ur 
    WHERE ur.user_id = users.id 
        AND ur.role = 'admin'
);
```

## Functions and Procedures

### Function Definition

```sql
CREATE OR REPLACE FUNCTION get_user_stats(user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
DECLARE
    result JSON;
    post_count INTEGER;
    follower_count INTEGER;
BEGIN
    -- Get post count
    SELECT COUNT(*)
    INTO post_count
    FROM public.posts
    WHERE author_id = user_id;
    
    -- Get follower count  
    SELECT COUNT(*)
    INTO follower_count
    FROM public.followers
    WHERE following_id = user_id;
    
    -- Build result
    SELECT json_build_object(
        'post_count', post_count,
        'follower_count', follower_count,
        'user_id', user_id
    ) INTO result;
    
    RETURN result;
END;
$$;
```

### Error Handling

```sql
-- Use meaningful error messages
IF NOT EXISTS (SELECT 1 FROM users WHERE id = user_id) THEN
    RAISE EXCEPTION 'User with ID % not found', user_id;
END IF;

-- Validate input parameters
IF email IS NULL OR email = '' THEN
    RAISE EXCEPTION 'Email cannot be null or empty';
END IF;
```

## Comments

### Documentation

```sql
-- Table documentation
-- Stores user profile information including avatar and bio
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    -- User's display name (publicly visible)
    display_name TEXT,
    -- Short biographical text (max 500 chars)
    bio TEXT CHECK (char_length(bio) <= 500),
    -- Profile image URL from storage
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Function documentation
/**
 * Calculates user engagement metrics
 * @param user_id UUID - The user's unique identifier
 * @param period_days INTEGER - Number of days to look back (default 30)
 * @returns JSON object with engagement stats
 */
CREATE OR REPLACE FUNCTION calculate_user_engagement(
    user_id UUID,
    period_days INTEGER DEFAULT 30
)
```

### Inline Comments

```sql
SELECT 
    u.id,
    u.email,
    -- Calculate days since last login
    EXTRACT(EPOCH FROM (now() - u.last_login_at))/86400 as days_since_login,
    -- Get total post count for user
    (
        SELECT COUNT(*) 
        FROM posts p 
        WHERE p.author_id = u.id
    ) as total_posts
FROM users u;
```

## Migrations

### Migration Structure

```sql
-- Migration: 20231201120000_add_user_profiles.sql

-- Add new table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    display_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add indexes
CREATE INDEX IF NOT EXISTS user_profiles_user_id_idx ON user_profiles(user_id);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view all profiles"
    ON user_profiles FOR SELECT
    USING (true);

CREATE POLICY "Users can update own profile"
    ON user_profiles FOR UPDATE
    USING (user_id = auth.uid());
```

## Best Practices

### Performance

```sql
-- Use appropriate indexes
CREATE INDEX users_email_idx ON users(email);
CREATE INDEX posts_author_created_idx ON posts(author_id, created_at);

-- Use EXPLAIN to analyze queries
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM users WHERE email = '<EMAIL>';

-- Limit result sets
SELECT * FROM posts ORDER BY created_at DESC LIMIT 50;
```

### Security

```sql
-- Always use parameterized queries (in application code)
-- Avoid: "SELECT * FROM users WHERE id = '" + user_id + "'"
-- Use: "SELECT * FROM users WHERE id = $1"

-- Use appropriate data types for validation
email TEXT CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
phone TEXT CHECK (phone ~* '^\+?[1-9]\d{1,14}$')

-- Set proper permissions
GRANT SELECT, INSERT, UPDATE ON users TO authenticated;
REVOKE ALL ON users FROM anon;
```

### Consistency

```sql
-- Use consistent naming throughout
-- Tables: plural (users, posts, comments)
-- Columns: singular (user_id, not users_id)
-- Timestamps: created_at, updated_at, deleted_at

-- Use consistent data types
-- UUIDs for IDs: UUID
-- Timestamps: TIMESTAMP WITH TIME ZONE  
-- Text: TEXT (not VARCHAR unless specific length needed)
-- Booleans: BOOLEAN (not integer flags)
```

