# Importing Tenant Listings

## Purpose
Keep the `tenant_listings` table in Supabase up-to-date with the master tenant list provided by management.

## Data Source
CSV file delivered periodically:
```
Properties_20250610045140991-Tenant Listing - MD(Sheet1) (1).csv
```

## Database Table
`public.tenant_listings`
| column         | type | notes                                |
|----------------|------|--------------------------------------|
| id             | uuid | primary key                          |
| tenant_name    | text |                                      |
| unit           | text | e.g. `105`, `B-24`, `Payment Agreement 1` |
| unit_type      | text | raw "Unit Type" from the sheet       |
| phone_default  | text |                                         |
| category       | text | only `Retail` or `Beauty`             |
| created_at     | timestamptz | default `now()`                |

RLS enabled – public **SELECT** policy only.

## Import Script
`scripts/importTenantListings.cjs`

```bash
# default path (expected file at project root)
npm run import-tenants

# custom path
npm run import-tenants ./data/latest-tenants.csv
```

Script behaviour:
1. Parses CSV lines (`;` delimited).
2. Ignores header rows.
3. Derives `category` from `Unit Type` (Beauty suite → Beauty, otherwise Retail).
4. Upserts into `tenant_listings` using `unit` for conflict resolution.

Environment variables required:
* `VITE_SUPABASE_URL` (or `SUPABASE_URL`)
* `SUPABASE_SERVICE_ROLE_KEY`

## Maintenance
1. Obtain the latest CSV from management.
2. Place it in the project root or pass a custom path.
3. Run the import script.
4. Verify data in Supabase dashboard.

---
_Last updated: 2025-06-28_ 