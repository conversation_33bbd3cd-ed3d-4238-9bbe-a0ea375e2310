import React from 'react';
import styled from 'styled-components';
import { logos } from '../../utils/logos';
import { motion } from 'framer-motion';

const Section = styled.section`
  padding: 4rem 1rem;
  text-align: center;
`;

const Title = styled.h2`
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #2C2C2C;
`;

const CarouselWrapper = styled.div`
  overflow: hidden;
  width: 100%;
`;

const CarouselTrack = styled(motion.div)`
  display: flex;
  gap: 4rem;
`;

const LogoCard = styled.div`
  flex: 0 0 auto;
  width: 200px;
  height: 150px; /* Tamaño fijo uniforme */
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;

  img {
    max-height: px; /* <PERSON><PERSON> solo alto */
    max-width: 100%;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter 0.3s ease-in-out;
  }

  &:hover img {
    filter: grayscale(0%);
  }
`;

export const Partners = () => {
  return (
    <Section>
      <Title>Nuestros Aliados</Title>
      <CarouselWrapper>
        <CarouselTrack
          animate={{ x: ['0%', '-50%'] }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear',
          }}
        >
          {[...logos, ...logos].map((logo, index) => (
            <LogoCard key={index}>
              <img src={logo.src} alt={logo.alt} loading="lazy" />
            </LogoCard>
          ))}
        </CarouselTrack>
      </CarouselWrapper>
    </Section>
  );
};
