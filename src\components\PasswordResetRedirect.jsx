import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

const PasswordResetRedirect = () => {
  const navigate = useNavigate()

  useEffect(() => {
    // Check if this is a password reset redirect
    const hashParams = new URLSearchParams(window.location.hash.substring(1))
    const type = hashParams.get('type')
    const accessToken = hashParams.get('access_token')
    
    if (type === 'recovery' && accessToken) {
      // Redirect to reset password page with the full hash
      navigate(`/reset-password${window.location.hash}`, { replace: true })
    }
  }, [navigate])

  return null // This component doesn't render anything
}

export default PasswordResetRedirect 