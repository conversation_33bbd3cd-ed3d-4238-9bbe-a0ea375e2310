import { useUserStore } from '../store/useUserStore';
import { supabase } from '../utils/supabaseClient';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

const useAnalytics = () => {
  const { standId: userStandId } = useUserStore();
  const location = useLocation();
  const [isRecording, setIsRecording] = useState(false);

  const recordEvent = async (eventType, userId = null, standId = null) => {
    try {
      const { error } = await supabase
        .from('stand_analytics')
        .insert([
          {
            stand_id: standId,
            event_type: eventType,
            user_id: userId
          }
        ]);

      if (error) throw error;
    } catch (error) {
      console.error('Error al registrar evento:', error);
    }
  };

  const recordVisit = async () => {
    if (location.pathname.includes('/stands-virtuales/') && !isRecording) {
      setIsRecording(true);
      // Obtener el slug de la URL
      const slug = location.pathname.split('/stands-virtuales/')[1];
      if (slug) {
        // Buscar primero por slug exacto
        const { data: standBySlug, error: slugError } = await supabase
          .from('stands')
          .select('id')
          .eq('slug', slug)
          .maybeSingle();

        if (slugError) {
          console.error(slugError);
        }

        let standData = standBySlug;

        // Fallback: intentar por coincidencia de nombre si no hay slug en la BD
        if (!standData) {
          const namePattern = slug.replace(/-/g, ' ');
          const { data: nameMatch, error: nameError } = await supabase
            .from('stands')
            .select('id')
            .ilike('name', `%${namePattern}%`)
            .maybeSingle();

          if (nameError) {
            console.error(nameError);
          }

          standData = nameMatch;
        }

        if (standData) {
          // Usar el stand_id del usuario si está logueado, si no usar el de la URL
          const standId = userStandId || standData.id;
          await recordEvent('visit', null, standId);
        }
      }
      // Resetear el estado después de un breve retraso
      setTimeout(() => setIsRecording(false), 1000);
    }
  };

  const recordAIInteraction = async () => {
    await recordEvent('ai_interaction', null, userStandId);
  };

  const recordProductView = async (userId = null) => {
    await recordEvent('product_view', userId);
  };

  // Registrar visita cuando cambia la ruta
  useEffect(() => {
    recordVisit();
  }, [location.pathname]);

  return {
    recordAIInteraction,
    recordProductView
  };
};

export default useAnalytics; 