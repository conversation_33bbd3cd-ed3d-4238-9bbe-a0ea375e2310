import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { exhibitors } from '../data/exhibitors';

const ExhibitorsContainer = styled.section`
  width: 100%;
  padding: 6rem 2rem;
  background: #f8f9fa;
`;

const Title = styled.h2`
  font-family: 'Bebas Neue', sans-serif;
  font-size: 3rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 2px;
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const ExhibitorCard = styled(motion.div)`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
  }
`;

const ImageContainer = styled.div`
  width: 100%;
  padding-top: 100%;
  position: relative;
  background: #e9ecef;
  overflow: hidden;
`;

const ExhibitorImage = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const PlaceholderImage = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #dee2e6;
  color: #6c757d;
  font-size: 1.5rem;
  font-weight: 500;
`;

const Info = styled.div`
  padding: 1.5rem;
  text-align: center;
`;

const Name = styled.h3`
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
`;

const Description = styled.p`
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
`;

const ExhibitorsGrid = () => {
  return (
    <ExhibitorsContainer>
      <Title>Nuestros Expositores</Title>
      <Grid>
        {exhibitors.map((exhibitor) => (
          <ExhibitorCard
            key={exhibitor.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <ImageContainer>
              {exhibitor.image ? (
                <ExhibitorImage 
                  src={exhibitor.image} 
                  alt={exhibitor.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.parentElement.querySelector('.placeholder').style.display = 'flex';
                  }}
                />
              ) : null}
              <PlaceholderImage className="placeholder" style={{ display: exhibitor.image ? 'none' : 'flex' }}>
                {exhibitor.name.charAt(0).toUpperCase()}
              </PlaceholderImage>
            </ImageContainer>
            <Info>
              <Name>{exhibitor.name}</Name>
              {exhibitor.description && (
                <Description>{exhibitor.description}</Description>
              )}
            </Info>
          </ExhibitorCard>
        ))}
      </Grid>
    </ExhibitorsContainer>
  );
};

export default ExhibitorsGrid; 