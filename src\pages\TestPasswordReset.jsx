import { useState } from 'react'
import styled from 'styled-components'
import { useAuthStore } from '../store/useAuthStore'
import { toast } from 'react-hot-toast'

const Container = styled.div`
  min-height: 100vh;
  padding: 2rem;
  background: #000;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`

const TestCard = styled.div`
  background: #1a1a1a;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #333;
  max-width: 600px;
  width: 100%;
`

const Title = styled.h1`
  color: #D8DF20;
  margin-bottom: 2rem;
  text-align: center;
`

const TestButton = styled.button`
  background: #D8DF20;
  color: #000;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  margin: 0.5rem;
  transition: all 0.2s;

  &:hover {
    background: #c5cc1d;
    transform: translateY(-2px);
  }

  &:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
  }
`

const ResultBox = styled.div`
  background: ${props => props.success ? '#d4edda' : '#f8d7da'};
  color: ${props => props.success ? '#155724' : '#721c24'};
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  border: 1px solid ${props => props.success ? '#c3e6cb' : '#f5c6cb'};
`

const InfoBox = styled.div`
  background: #d1ecf1;
  color: #0c5460;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  border: 1px solid #b6d4db;
`

const TestPasswordReset = () => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const requestPasswordReset = useAuthStore((state) => state.requestPasswordReset)

  const testEmail = '<EMAIL>'

  const testPasswordRecovery = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('🔄 Enviando email de recuperación a:', testEmail)
      
      const { error } = await requestPasswordReset(testEmail)
      
      if (error) {
        setResult({
          success: false,
          message: `Error: ${error}`
        })
        toast.error('Error al enviar email de recuperación')
      } else {
        setResult({
          success: true,
          message: `✅ Email de recuperación enviado exitosamente a ${testEmail}`
        })
        toast.success('¡Email enviado!')
      }
    } catch (err) {
      setResult({
        success: false,
        message: `Error inesperado: ${err.message}`
      })
      toast.error('Error inesperado')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container>
      <TestCard>
        <Title>🔧 Test Password Recovery</Title>
        
        <InfoBox>
          <strong>Usuario de prueba:</strong> {testEmail}
          <br />
          <strong>Propósito:</strong> Verificar que el sistema de recuperación de contraseñas funcione correctamente.
          <br />
          <strong>Pasos:</strong>
          <ol>
            <li>Hacer clic en "Enviar Email de Recuperación"</li>
            <li>Revisar el email en la bandeja de entrada de {testEmail}</li>
            <li>Hacer clic en el enlace del email</li>
            <li>Establecer nueva contraseña</li>
          </ol>
        </InfoBox>

        <TestButton 
          onClick={testPasswordRecovery}
          disabled={loading}
        >
          {loading ? 'Enviando...' : 'Enviar Email de Recuperación'}
        </TestButton>

        {result && (
          <ResultBox success={result.success}>
            {result.message}
          </ResultBox>
        )}

        {result?.success && (
          <InfoBox>
            <strong>🎯 Próximos pasos:</strong>
            <br />
            1. Revisa el email en {testEmail}
            <br />
            2. Haz clic en el enlace "Reset Password"
            <br />
            3. Serás redirigido a /reset-password
            <br />
            4. Establece una nueva contraseña
            <br />
            5. Inicia sesión con la nueva contraseña
          </InfoBox>
        )}

        <div style={{ marginTop: '2rem', textAlign: 'center' }}>
          <a href="/login" style={{ color: '#D8DF20', textDecoration: 'none' }}>
            ← Volver al Login
          </a>
        </div>
      </TestCard>
    </Container>
  )
}

export default TestPasswordReset 