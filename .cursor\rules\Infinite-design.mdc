---
description: 
globs: 
alwaysApply: true
---
DESIGN VARIATIONS GENERATOR
Generate n = 3 files
Instructions
Look at the design that has been attached.
Create 3 different versions of that same design. Make each one look different but keep the same basic idea and functionality.
Follow any additional instructions given in the prompt.
File Management
Check if a "variations" folder exists
If it exists:
Rename the reference design to "source.html"
Delete any other files in the folder
If "source.html" already exists, delete the old one first
If no "variations" folder exists:
Create the "variations" folder
Put the reference design in as "source.html"
Create Files
Make these files in the variations folder:
design1.html
design2.html
design3.html
Continue to design{n}.html if more than 3 requested
Each design should be completely different visually but work the same way.
That's it.
Keep it simple. Make them look different. Follow any extra instructions given.

