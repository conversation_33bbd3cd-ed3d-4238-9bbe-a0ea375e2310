import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useLanguageStore } from '../store/useLanguageStore';
import { FaUtensils, FaSpa, FaShoppingBag, FaCalendarAlt } from 'react-icons/fa';
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';

const Section = styled.section`
  width: 100%;
  background: #f8f6ea;
  padding: 3rem 0 2rem 0;
  overflow: hidden;
`;

const Title = styled.h2`
  font-family: 'Bebas Neue', sans-serif;
  font-size: 2.2rem;
  color: #f16925;
  font-weight: 900;
  margin-bottom: 2rem;
  letter-spacing: 1px;
  text-align: left;
  padding-left: 2rem;
`;

const CarouselWrapper = styled.div`
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding-bottom: 1rem;
`;

const CardsRow = styled(motion.div)`
  display: flex;
  gap: 1.5rem;
  min-width: 700px;
  padding-left: 2rem;
`;

const Card = styled.div`
  min-width: 220px;
  height: 180px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: #23281d;
  font-weight: 600;
`;

const categoryColors = [
  '#FFD541', // Amarillo
  '#8740B3', // Morado
  '#3256A1', // Azul
  '#37B9D3', // Turquesa
];

const CategoriesContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2.5rem;
  margin-bottom: 2.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;

  @media (max-width: 600px) {
    gap: 1.2rem;
    margin-bottom: 1.2rem;
  }
`;

const CategoryIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 18px;
  background: ${({ color }) => color || '#fff'};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
  font-size: 2.2rem;
  position: relative;

  &:hover {
    transform: scale(1.12);
    filter: brightness(1.1);
  }

  @media (max-width: 600px) {
    width: 54px;
    height: 54px;
    font-size: 1.3rem;
  }
`;

const CategoryLabel = styled.span`
  display: block;
  margin-top: 0.5rem;
  font-size: 1.05rem;
  color: #23281d;
  font-weight: 600;
  text-align: center;
  @media (max-width: 600px) {
    font-size: 0.85rem;
  }
`;

const NavigationButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
`;

const NavButton = styled.button`
  background: #f16925;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: #d45a1f;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const ViewMarketsRecientes = () => {
  const { language } = useLanguageStore();
  const [currentIndex, setCurrentIndex] = useState(0);
  const totalCards = 6;
  const cardsPerView = 3;

  const categories = [
    { icon: <FaUtensils />, label: language === 'es' ? 'Comida' : 'Eat', color: categoryColors[0] },
    { icon: <FaSpa />, label: language === 'es' ? 'Belleza' : 'Beauty', color: categoryColors[1] },
    { icon: <FaShoppingBag />, label: language === 'es' ? 'Tienda' : 'Shop', color: categoryColors[2] },
    { icon: <FaCalendarAlt />, label: language === 'es' ? 'Eventos' : 'Events', color: categoryColors[3] },
  ];

  const handlePrev = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => Math.min(totalCards - cardsPerView, prev + 1));
  };

  return (
    <Section>
      <CategoriesContainer>
        {categories.map((cat, idx) => (
          <div key={idx} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <CategoryIcon color={cat.color} title={cat.label}>
              {cat.icon}
            </CategoryIcon>
            <CategoryLabel>{cat.label}</CategoryLabel>
          </div>
        ))}
      </CategoriesContainer>
      <Title>{language === 'es' ? 'Ver Markets Recientes' : 'View Recent Markets'}</Title>
      
      <CarouselWrapper>
        <CardsRow
          style={{
            transform: `translateX(-${currentIndex * (220 + 24)}px)`,
            transition: 'transform 0.3s ease'
          }}
        >
          {[1,2,3,4,5,6].map((i) => (
            <Card key={i}>
              {language === 'es' ? 'Market' : 'Market'} #{i}
            </Card>
          ))}
        </CardsRow>
      </CarouselWrapper>

      <NavigationButtons>
        <NavButton onClick={handlePrev} disabled={currentIndex === 0}>
          <IoIosArrowBack size={20} />
        </NavButton>
        <NavButton onClick={handleNext} disabled={currentIndex >= totalCards - cardsPerView}>
          <IoIosArrowForward size={20} />
        </NavButton>
      </NavigationButtons>
    </Section>
  );
};

export default ViewMarketsRecientes; 