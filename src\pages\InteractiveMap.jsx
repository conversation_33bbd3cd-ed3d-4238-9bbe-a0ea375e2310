import React, { useState } from 'react';
import styled from 'styled-components';
import { MapSidebar } from '../components/MapSidebar';
import { MapCanvas } from '../components/MapCanvas';
import { Navbar } from '../components/Navbar'; // <-- Importar tu Navbar

const PageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
`;

const Layout = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
  }
`;

export const InteractiveMap = () => {
  const [selectedMarker, setSelectedMarker] = useState(null);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Todos');

  return (
    <PageWrapper>
      <Navbar /> {/* Navbar arriba */}
      <Layout>
        <MapSidebar
          search={search}
          setSearch={setSearch}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          setSelectedMarker={setSelectedMarker}
        />
        <MapCanvas
          selectedMarker={selectedMarker}
          setSelectedMarker={setSelectedMarker}
          filterCategory={selectedCategory !== 'Todos' ? selectedCategory : null}
          search={search}
        />
      </Layout>
    </PageWrapper>
  );
};
