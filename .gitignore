# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
jspm_packages/

# Build output
dist/
dist-ssr/
build/
out/
coverage/
.nyc_output/

# Local environment files
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# Environment template with credentials
env.template

# Cursor MCP configuration (contains sensitive tokens)
.cursor/mcp.json

# Miscellaneous
*.local
*.cache
.cache/
.tmp/
temp/
*.bak
*.swp
*.swo

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# OS-specific files
.DS_Store
Thumbs.db
ehthumbs.db
Icon?
Desktop.ini

# Test output
test-output/
junit.xml

# Others
*.iml
*.log.[0-9]*
npm-debug.log*
yarn-error.log*
lerna-debug.log*
