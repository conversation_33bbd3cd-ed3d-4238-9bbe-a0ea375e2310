-- =============================================================================
-- CREATE LOCAL BUSINESS USER WITH STAND
-- =============================================================================
-- This script creates a complete local business user with:
-- 1. Authentication records (auth.users + auth.identities)
-- 2. Public profile (public.users)
-- 3. Business stand (public.stands)
--
-- INSTRUCTIONS:
-- 1. Replace the variables in the DECLARE section with actual values
-- 2. Run this script in Supabase SQL Editor
-- 3. User will be ready for login immediately
-- =============================================================================

DO $$
DECLARE
    -- =========================================================================
    -- CONFIGURE THESE VALUES FOR EACH NEW USER
    -- =========================================================================
    user_email TEXT := '<EMAIL>';           -- User's email address
    user_password TEXT := 'Valores7%';                -- User's password
    user_first_name TEXT := 'Business';               -- User's first name
    user_last_name TEXT := 'Owner';                   -- User's last name
    
    -- Stand/Business Information
    stand_name TEXT := 'Example Business';            -- Business name
    stand_slug TEXT := 'example-business';            -- URL-friendly slug (lowercase, hyphens)
    stand_description TEXT := 'Description of the business'; -- Business description
    stand_category TEXT := 'Restaurant';              -- Business category
    
    -- =========================================================================
    -- INTERNAL VARIABLES (DO NOT MODIFY)
    -- =========================================================================
    new_user_id UUID;
    new_stand_id UUID;
    encrypted_pass TEXT;
BEGIN
    -- Generate UUIDs
    new_user_id := gen_random_uuid();
    new_stand_id := gen_random_uuid();
    
    -- Encrypt password
    encrypted_pass := crypt(user_password, gen_salt('bf'));
    
    -- Check if email already exists
    IF EXISTS (SELECT 1 FROM auth.users WHERE email = user_email) THEN
        RAISE EXCEPTION 'User with email % already exists', user_email;
    END IF;
    
    -- =========================================================================
    -- CREATE AUTH.USERS RECORD
    -- =========================================================================
    INSERT INTO auth.users (
        instance_id,
        id,
        aud,
        role,
        email,
        encrypted_password,
        email_confirmed_at,
        email_change_sent_at,
        last_sign_in_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        created_at,
        updated_at,
        phone,
        phone_confirmed_at,
        confirmation_token,
        recovery_token,
        email_change_token_new,
        email_change
    ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        new_user_id,
        'authenticated',
        'authenticated',
        user_email,
        encrypted_pass,
        NOW(),
        NULL,
        NULL,
        '{"provider": "email", "providers": ["email"]}'::jsonb,
        ('{"email": "' || user_email || '", "email_verified": true, "phone_verified": false, "sub": "' || new_user_id || '"}')::jsonb,
        false,
        NOW(),
        NOW(),
        NULL,
        NULL,
        '',
        '',
        '',
        ''
    );
    
    -- =========================================================================
    -- CREATE AUTH.IDENTITIES RECORD
    -- =========================================================================
    INSERT INTO auth.identities (
        provider_id,
        user_id,
        identity_data,
        provider,
        last_sign_in_at,
        created_at,
        updated_at,
        id
    ) VALUES (
        new_user_id::text,
        new_user_id,
        ('{"email": "' || user_email || '", "email_verified": true, "phone_verified": false, "sub": "' || new_user_id || '"}')::jsonb,
        'email',
        NULL,
        NOW(),
        NOW(),
        gen_random_uuid()
    );
    
    -- =========================================================================
    -- CREATE PUBLIC.USERS RECORD
    -- =========================================================================
    INSERT INTO public.users (
        id,
        created_at,
        name,
        lastname,
        points,
        role,
        email,
        avatar_url,
        phone,
        rol
    ) VALUES (
        new_user_id,
        NOW(),
        user_first_name,
        user_last_name,
        0,
        'local',
        user_email,
        NULL,
        NULL,
        'local'
    );
    
    -- =========================================================================
    -- CREATE PUBLIC.STANDS RECORD
    -- =========================================================================
    INSERT INTO public.stands (
        id,
        name,
        slug,
        description,
        category,
        email,
        user_id,
        created_at,
        updated_at
    ) VALUES (
        new_stand_id,
        stand_name,
        stand_slug,
        stand_description,
        stand_category,
        user_email,
        new_user_id,
        NOW(),
        NOW()
    );
    
    -- =========================================================================
    -- SUCCESS MESSAGE
    -- =========================================================================
    RAISE NOTICE '✅ SUCCESS: User created successfully!';
    RAISE NOTICE 'User ID: %', new_user_id;
    RAISE NOTICE 'Stand ID: %', new_stand_id;
    RAISE NOTICE 'Email: %', user_email;
    RAISE NOTICE 'Stand Name: %', stand_name;
    RAISE NOTICE 'Password: %', user_password;
    RAISE NOTICE '';
    RAISE NOTICE '🔐 LOGIN CREDENTIALS:';
    RAISE NOTICE 'Email: %', user_email;
    RAISE NOTICE 'Password: %', user_password;
    RAISE NOTICE '';
    RAISE NOTICE '✨ User is ready for login!';
    
END $$;

-- =============================================================================
-- VERIFICATION QUERY (Optional - run after the script above)
-- =============================================================================
-- Uncomment and run this query to verify the user was created correctly:

/*
SELECT 
    'User Info' as section,
    u.email,
    u.name || ' ' || u.lastname as full_name,
    u.role,
    u.rol,
    'Ready for login' as status
FROM public.users u
WHERE u.email = '<EMAIL>'  -- Replace with actual email

UNION ALL

SELECT 
    'Stand Info' as section,
    s.email,
    s.name as stand_name,
    s.slug,
    s.category,
    s.description
FROM public.stands s
WHERE s.email = '<EMAIL>'  -- Replace with actual email

ORDER BY section;
*/ 