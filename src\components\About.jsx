import React from 'react';
import styled from 'styled-components';
import { useLanguageStore } from '../store/useLanguageStore';

const AboutSection = styled.section`
  width: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  position: relative;
  overflow: hidden;
  
  /* Blurred background image */
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    background: url('/people/DSCF5301.webp') center/cover no-repeat;
    background-size: cover;
    filter: none;
    z-index: 1;
  }
  
  /* Orange overlay for brand consistency */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(241, 105, 37, 0.85), rgba(241, 105, 37, 0.75));
    z-index: 2;
  }
`;

const AboutContent = styled.div`
  max-width: 1200px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 3rem;
  position: relative;
  z-index: 3;
  padding: 0 2rem;
  
  @media (max-width: 900px) {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
    padding: 0 1rem;
  }
`;

const AboutText = styled.div`
  flex: 2;
`;

const AboutTitle = styled.h2`
  font-family: 'Bebas Neue', sans-serif;
  font-size: 3.5rem;
  color: #ffffff;
  font-weight: 900;
  margin-bottom: 1.5rem;
  letter-spacing: 2px;
  text-align: left;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.1;
  
  @media (max-width: 900px) {
    text-align: center;
    font-size: 2.8rem;
  }
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const AboutDesc = styled.p`
  font-size: 1.3rem;
  color: #ffffff;
  margin-bottom: 0;
  line-height: 1.7;
  font-weight: 400;
  text-align: left;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  
  @media (max-width: 900px) {
    text-align: center;
    font-size: 1.2rem;
  }
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
`;

const AboutLogo = styled.img`
  flex: 1;
  max-width: 200px;
  max-height: 200px;
  object-fit: contain;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
  
  @media (max-width: 768px) {
    max-width: 150px;
    max-height: 150px;
  }
`;

const About = () => {
  const { language } = useLanguageStore();
  return (
    <AboutSection>
      <AboutContent>
        <AboutLogo src="/logo-2.webp" alt="Food Market Hall Logo" loading="lazy" />
        <AboutText>
          <AboutTitle>
            {language === 'es' ? 'Descubre Food Market Hall and Shops' : 'Your Ultimate Destination for Shopping, Dining, and Live Music in McAllen'}
          </AboutTitle>
          <AboutDesc>
            {language === 'es'
              ? 'Mercado Food Hall and Shops es el destino donde la innovación, la gastronomía y el comercio se unen. Nuestra plataforma, impulsada por IA, te permite encontrar fácilmente restaurantes, tiendas y eventos en un solo lugar. Vive la experiencia de una plaza comercial moderna, donde cada visita es una celebración de sabores, compras y momentos únicos.'
              : 'Mercado Food Hall and Shops is McAllen\'s newest destination for shopping, dining, and live music. Enjoy local bands from Tuesday to Sunday while exploring a variety of shops and restaurants. Discover the best of McAllen\'s food, shopping, and entertainment all in one place.'}
          </AboutDesc>
        </AboutText>
      </AboutContent>
    </AboutSection>
  );
};

export default About; 