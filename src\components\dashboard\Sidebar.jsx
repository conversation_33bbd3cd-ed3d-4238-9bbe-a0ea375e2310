import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Edit2, Package, Bot, ArrowLeft, Home, Gift, History } from 'lucide-react';
import { useLanguageStore } from '../../store/useLanguageStore';

const SidebarContainer = styled(motion.aside)`
  width: 15%;
  height: 100%;
  position: fixed;
  background: #000;
  padding: 1.5rem;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);

  @media (max-width: 1024px) {
    display: none;
  }
`;

const LogoWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: .5rem;
  margin-bottom: .5rem;
  border-bottom: 1.5px solid rgba(255,255,255,0.12);
`;

const Logo = styled.img`
  width: 100px;
  height: 100px;
  object-fit: contain;
`;

const NavList = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  margin-top: 1rem;
`;

const NavItem = styled(Link)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  color: ${props => props.$active ? '#F16925' : 'rgba(255,255,255,0.85)'};
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  background: ${props => props.$active ? 'rgba(241,105,37,0.12)' : 'transparent'};
  border: 1.5px solid ${props => props.$active ? '#F16925' : 'transparent'};

  &:hover {
    background: rgba(241,105,37,0.12);
    color: #fff;
    transform: translateX(5px);
  }

  svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: ${props => props.$active ? '#F16925' : 'rgba(255,255,255,0.7)'};
    transition: color 0.3s;
  }
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #F16925 0%, #e05a1a 100%);
  color: #fff;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  width: 100%;
  border: 2px solid transparent;
  box-shadow: 0 4px 12px rgba(241, 105, 37, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    background: linear-gradient(135deg, #e05a1a 0%, #d64f15 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(241, 105, 37, 0.4);
    border-color: rgba(255,255,255,0.1);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(241, 105, 37, 0.3);
  }

  svg {
    width: 18px;
    height: 18px;
    color: #fff;
    transition: transform 0.3s ease;
    flex-shrink: 0;
  }

  &:hover svg {
    transform: translateX(-2px);
  }

  span {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
`;

const Sidebar = () => {
  const location = useLocation();
  const { language } = useLanguageStore();

  return (
    <SidebarContainer>
      <LogoWrapper>
        <Logo src="/logo.webp" alt="Logo" loading="eager" fetchpriority="high" />
      </LogoWrapper>

      <NavList>
        <NavItem
          to="/dashboard"
          $active={location.pathname === '/dashboard'}
        >
          <Home size={20} />
          <span>{language === 'es' ? 'Inicio' : 'Home'}</span>
        </NavItem>

        <NavItem
          to="/dashboard/edit"
          $active={location.pathname === '/dashboard/edit'}
        >
          <Edit2 size={20} />
          <span>{language === 'es' ? 'Editar Perfil' : 'Edit Profile'}</span>
        </NavItem>

        <NavItem
          to="/dashboard/products"
          $active={location.pathname === '/dashboard/products'}
        >
          <Package size={20} />
          <span>{language === 'es' ? 'Productos' : 'Products'}</span>
        </NavItem>

        <NavItem
          to="/dashboard/ai"
          $active={location.pathname === '/dashboard/ai'}
        >
          <Bot size={20} />
          <span>{language === 'es' ? 'IA' : 'AI'}</span>
        </NavItem>

        <NavItem
          to="/dashboard/points"
          $active={location.pathname === '/dashboard/points'}
        >
          <Gift size={20} />
          <span>{language === 'es' ? 'Puntos' : 'Points'}</span>
        </NavItem>

        <NavItem
          to="/dashboard/points-history"
          $active={location.pathname === '/dashboard/points-history'}
        >
          <History size={20} />
          <span>{language === 'es' ? 'Historial' : 'History'}</span>
        </NavItem>
      </NavList>

      <BackButton to="/">
        <ArrowLeft size={20} />
        <span>{language === 'es' ? 'Volver al Inicio' : 'Back to Home'}</span>
      </BackButton>
    </SidebarContainer>
  );
};

export default Sidebar;
