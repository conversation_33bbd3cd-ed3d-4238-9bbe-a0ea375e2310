app.post('/api', async (req, res) => {
  const { pregunta, stand_slug } = req.body;
  console.log('POST /api - Pregunta recibida:', pregunta);
  console.log('Stand slug:', stand_slug);

  if (!pregunta) {
    return res.status(400).json({ error: 'La pregunta es requerida' });
  }

  try {
    // 🔍 Consultar Supabase para obtener el contexto específico del stand
    const { data: stand, error: standError } = await supabase
      .from('stands')
      .select('ai_stands')
      .eq('slug', stand_slug)
      .single();

    if (standError) {
      console.error('Error consultando Supabase:', standError);
      return res.status(500).json({ error: 'Error al consultar Supabase' });
    }

    if (!stand) {
      return res.status(404).json({ error: 'Stand no encontrado' });
    }

    const contexto = stand.ai_stands;

    // 🧠 Construcción del mensaje para Groq
    const messages = [
      {
        role: "system",
        content: `Eres un asistente virtual especializado en proporcionar información sobre el stand específico. 
                 Basa tus respuestas en el contexto proporcionado y mantén un tono amigable y profesional.
                 Si la pregunta no está relacionada con el stand, indícalo amablemente.`
      },
      {
        role: "user",
        content: `Contexto del stand:\n${contexto}\n\nPregunta del usuario:\n${pregunta}`
      }
    ];

    // 📡 Llamada a Groq
    const response = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: 'llama-3.3-70b-versatile',
        messages: messages,
        temperature: 0.7,
        max_tokens: 500,
        top_p: 1,
        stream: false
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const respuesta = response.data.choices[0].message.content.trim();
    console.log('Respuesta generada correctamente');

    res.status(200).json({ respuesta });

  } catch (err) {
    console.error('Error general:', err.response?.data || err.message);
    res.status(500).json({
      error: 'Error interno del servidor',
      details: err.response?.data || err.message
    });
  }
}); 