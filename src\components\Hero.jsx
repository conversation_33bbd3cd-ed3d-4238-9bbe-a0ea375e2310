import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguageStore } from '../store/useLanguageStore';

const HeroContainer = styled.section`
  position: relative;
  width: 100%;
  height: 75vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0 3rem;
  @media (max-width: 900px) {
    padding: 0 2rem;
  }
  @media (max-width: 600px) {
    padding: 0 1rem;
  }
`;

const VideoBackground = styled.video`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
`;

const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2;
`;

const CenteredTitle = styled.h1`
  position: relative;
  z-index: 3;
  color: #fff;
  font-family: '<PERSON>bas Neue', sans-serif;
  font-size: 8rem;
  text-align: center;
  letter-spacing: 2px;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
  margin: 0;
  /* Always visible */
  opacity: 1;
  transform: translateY(0);
  transition: none;
  @media (max-width: 768px) {
    font-size: 4rem;
  }
`;

const MarketsButton = styled.button`
  margin-top: 2.5rem;
  background: transparent;
  color: #fff;
  border: 3px solid #fff;
  border-radius: 10px;
  padding: 1rem 3rem;
  font-size: 2rem;
  font-family: 'Bebas Neue', sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.8s ease-out;
  z-index: 3;
  /* Always visible */
  opacity: 1;
  transform: translateY(0);
  &:hover {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: #fff;
  }
`;

const Hero = () => {
  const { language } = useLanguageStore();
  const navigate = useNavigate();
  const videoRef = useRef(null);

  const handleExploreClick = () => {
    navigate('/stands-virtuales');
  };

  useEffect(() => {
    const video = videoRef.current;

    const handleTimeUpdate = () => {
      if (video.currentTime >= 15) {
        video.currentTime = 0;
      }
    };

    video.addEventListener('timeupdate', handleTimeUpdate);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, []);

  return (
    <HeroContainer>
      <VideoBackground
        ref={videoRef}
        autoPlay
        loop
        muted
        playsInline
        preload="auto" /* ensures the browser starts fetching ASAP */
        poster="/banner-mall.webp" /* quick visual while first frame decodes */
        crossOrigin="anonymous"
        fetchPriority="high"
        src="/video.mp4"
      >
        {/* Accessibility: Provide an empty captions track to satisfy a11y audits.
            The video is purely decorative (muted background), so the caption
            simply describes the ambience. */}
        <track
          kind="captions"
          src="/video.vtt"
          srcLang="en"
          label="English captions"
        />
      </VideoBackground>
      <Overlay />
      <CenteredTitle>Mercado Food Hall & Shops</CenteredTitle>
      <MarketsButton onClick={handleExploreClick}>
        {language === 'es' ? 'Explorar' : 'Explore'}
      </MarketsButton>
    </HeroContainer>
  );
};

export default Hero;
