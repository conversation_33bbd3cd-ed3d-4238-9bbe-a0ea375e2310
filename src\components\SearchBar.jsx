import React from 'react';
import styled from 'styled-components';
import { Search } from 'lucide-react';
import { motion } from 'framer-motion';

const Wrapper = styled.div`
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  padding: 0vh 6vw;
`;

const Title = styled.h3`
  font-size: 2rem;
  color: #2C2C2C;
  margin-bottom: 1rem;
  text-align: center;
  transition: all 0.3s ease;
`;

const NoResults = styled.p`
  text-align: center;
  font-size: 2.5rem;
  color: #999;
  margin-top: 1rem;

  strong {
    color: #333;
  }
`;



const SearchContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 1.5rem 1rem;
  margin: 2rem 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;

  &:focus-within {
    box-shadow: 0 0 0 3px rgba(50, 234, 153, 0.4); /* verde suave */
  }
`;

const StyledIcon = styled(Search)`
  margin-right: 0.75rem;
  color: #999;
`;

const Input = styled.input`
  flex: 1;
  font-size: 1rem;
  border: none;
  outline: none;
  color: #333;
  background: transparent;

  &::placeholder {
    color: #aaa;
  }
`;

export const SearchBar = ({ value, onChange, showNoResults = false }) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      console.log('Buscar:', value);
    }
  };

  const formattedTitle = value.trim()
    ? `Buscando: "${value}"`
    : 'Buscador';

  return (
    <Wrapper>
      <Title>{formattedTitle}</Title>
      <SearchContainer
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <StyledIcon size={20} />
        <Input
          type="text"
          placeholder="Buscar secciones..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyPress}
        />
      </SearchContainer>
      {showNoResults && value.trim() && (
    <NoResults>
        No existen resultados para "<strong>{value}</strong>"
    </NoResults>
)}
    </Wrapper>
  );
};