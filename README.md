# EME Fest - Plataforma Virtual de Eventos

## 🎉 Descripción
EME Fest es una plataforma virtual moderna para la gestión y visualización de eventos, stands virtuales y experiencias interactivas. La plataforma permite a los usuarios explorar stands virtuales, gestionar itinerarios y participar en actividades del evento.

## 🚀 Características Principales

### 👥 Gestión de Usuarios
- Sistema de autenticación con Supabase
- Roles diferenciados (usuario, stand)
- Perfiles personalizados
- Sistema de puntos

### 🏢 Stands Virtuales
- Visualización detallada de stands
- Chatbot integrado para atención al visitante
- Información detallada de productos y servicios
- Interfaz interactiva y moderna

### 🗺️ Mapa Interactivo
- Visualización de ubicaciones
- Navegación intuitiva
- Información detallada de espacios

### 📱 Otras Funcionalidades
- Itinerario personalizado
- Sistema de puntos Revive+
- Consulta de puntos acumulados
- Panel de control para stands

## 🛠️ Tecnologías Utilizadas

### Frontend
- React.js
- Vite
- Styled Components
- Framer Motion
- Zustand (Gestión de Estado)
- React Router

### Backend
- Supabase
- Node.js
- Express
- Groq API (Chatbot)

## 📦 Instalación

1. Clonar el repositorio:
```bash
git clone https://github.com/tu-usuario/eme-fest.git
cd eme-fest
```

2. Instalar dependencias:
```bash
npm install
```

3. Configurar variables de entorno:
```bash
cp .env.example .env
```
Editar el archivo `.env` con tus credenciales de Supabase y otras APIs.

4. Iniciar el servidor de desarrollo:
```bash
npm run dev
```

## 🔧 Configuración

### Variables de Entorno
```env
VITE_SUPABASE_URL=tu_url_de_supabase
VITE_SUPABASE_ANON_KEY=tu_clave_anonima
VITE_GROQ_API_KEY=tu_clave_de_groq
```

## 📁 Estructura del Proyecto

```
eme-fest/
├── src/
│   ├── components/
│   │   ├── ui/
│   │   ├── ChatbotWidget/
│   │   └── Navbar/
│   ├── pages/
│   ├── store/
│   ├── utils/
│   └── App.jsx
├── public/
├── index.html
└── package.json
```

## 🤖 Chatbot
El chatbot integrado utiliza la API de Groq para proporcionar respuestas contextuales basadas en la información del stand. Se integra perfectamente con la interfaz de usuario y proporciona una experiencia fluida para los visitantes.

## 👥 Roles de Usuario

### Usuario Regular
- Explorar stands virtuales
- Interactuar con chatbots
- Gestionar itinerario
- Acumular puntos

### Usuario Stand
- Panel de control personalizado
- Gestión de información del stand
- Estadísticas de visitas
- Interacción con visitantes

## 🔒 Seguridad
- Autenticación segura con Supabase
- Protección de rutas
- Manejo seguro de sesiones
- Validación de roles

## 📱 Responsive Design
La aplicación está completamente optimizada para:
- Móviles
- Tablets
- Escritorio
- Pantallas grandes

## 🤝 Contribución
1. Fork el proyecto
2. Crear una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abrir un Pull Request

## 📄 Licencia
Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE.md](LICENSE.md) para más detalles.

## 👥 Autores
- Tu Nombre - [@tu-usuario](https://github.com/tu-usuario)

## 🙏 Agradecimientos
- Supabase por la infraestructura de backend
- Groq por la API de IA
- Todos los contribuidores del proyecto
