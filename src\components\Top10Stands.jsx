import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Star, MapPin } from 'lucide-react';
import { useTop10Store } from '../store/useTop10Store';
import { useLanguageStore } from '../store/useLanguageStore';
import Loader from './Loader';

const Section = styled.section`
  width: 100%;
  background: #f9f9f9;
  padding: 5rem 0;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: 3.5rem 0;
  }
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 4rem;
  position: relative;

  @media (max-width: 768px) {
    margin-bottom: 3rem;
  }
`;

const Title = styled.h2`
  font-family: '<PERSON><PERSON> Neue', sans-serif;
  font-size: 3.5rem;
  color: #222;
  font-weight: 700;
  margin-bottom: 0.8rem;
  letter-spacing: 2px;
  text-transform: uppercase;

  &::after {
    content: '';
    display: block;
    margin: 8px auto 0;
    width: 70px;
    height: 3px;
    background: #f16925;
    border-radius: 2px;
  }

  @media (max-width: 768px) {
    font-size: 2.5rem;
    
    &::after {
      width: 50px;
    }
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  /* Darker shade for sufficient contrast on light background */
  color: #444;
  font-weight: 400;

  @media (max-width: 768px) {
    font-size: 1rem;
    padding: 0 1rem;
  }
`;

const CarouselContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  height: 550px;

  @media (max-width: 768px) {
    gap: 1rem;
    height: 450px;
  }

  @media (max-width: 480px) {
    height: 400px;
  }
`;

const CarouselWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CarouselTrack = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const NavigationButton = styled.button`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  z-index: 10;

  &:hover {
    background: #f16925;
    color: #fff;
    border-color: #f16925;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(241, 105, 37, 0.2);
  }

  &:disabled {
    background: #f0f0f0;
    color: #aaa;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    border-color: #e0e0e0;
  }

  @media (max-width: 768px) {
    width: 45px;
    height: 45px;
  }
`;

const RestaurantCard = styled.div`
  position: absolute;
  width: ${props => props.isCenter ? '340px' : '280px'};
  height: ${props => props.isCenter ? '480px' : '400px'};
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: ${props => props.isCenter 
    ? '0 15px 40px rgba(0, 0, 0, 0.1)'
    : '0 8px 25px rgba(0, 0, 0, 0.08)'};
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  display: flex;
  flex-direction: column;
  transform: ${props => {
    if (props.isCenter) return 'translateX(-50%) scale(1)';
    if (props.position === 'left') return 'translateX(-85%) scale(0.85)';
    if (props.position === 'right') return 'translateX(-15%) scale(0.85)';
    return 'translateX(-50%) scale(0.7)';
  }};
  left: ${props => {
    if (props.isCenter) return '50%';
    if (props.position === 'left') return '25%';
    if (props.position === 'right') return '75%';
    return '50%';
  }};
  opacity: ${props => props.isCenter ? 1 : 0.6};
  z-index: ${props => props.isCenter ? 5 : props.position === 'left' || props.position === 'right' ? 3 : 1};

  &:hover {
    transform: ${props => {
      if (props.isCenter) return 'translateX(-50%) scale(1.03) translateY(-6px)';
      if (props.position === 'left') return 'translateX(-85%) scale(0.88) translateY(-3px)';
      if (props.position === 'right') return 'translateX(-15%) scale(0.88) translateY(-3px)';
      return 'translateX(-50%) scale(0.72) translateY(-3px)';
    }};
    box-shadow: ${props => props.isCenter 
      ? '0 20px 50px rgba(0, 0, 0, 0.12)'
      : '0 12px 35px rgba(0, 0, 0, 0.1)'};
    opacity: ${props => props.isCenter ? 1 : 0.9};
  }

  @media (max-width: 768px) {
    width: ${props => props.isCenter ? '300px' : '250px'};
    height: ${props => props.isCenter ? '420px' : '360px'};
    border-radius: 16px;

    transform: ${props => {
      if (props.isCenter) return 'translateX(-50%) scale(1)';
      if (props.position === 'left') return 'translateX(-75%) scale(0.8)';
      if (props.position === 'right') return 'translateX(-25%) scale(0.8)';
      return 'translateX(-50%) scale(0.6)';
    }};
  }

  @media (max-width: 480px) {
    width: ${props => props.isCenter ? '270px' : '210px'};
    height: ${props => props.isCenter ? '380px' : '320px'};

    transform: ${props => {
      if (props.isCenter) return 'translateX(-50%) scale(1)';
      return 'translateX(-50%) scale(0.7)';
    }};

    left: 50%;
  }
`;

const RestaurantBanner = styled.div`
  height: ${props => props.isCenter ? '280px' : '220px'};
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(241, 105, 37, 0.1) 0%,
      rgba(216, 223, 32, 0.1) 100%
    );
    z-index: 1;
  }

  @media (max-width: 768px) {
    height: ${props => props.isCenter ? '240px' : '180px'};
  }

  @media (max-width: 480px) {
    height: ${props => props.isCenter ? '200px' : '160px'};
  }
`;

const RestaurantNumber = styled.div`
  position: absolute;
  top: 16px;
  left: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  color: #f16925;
  font-weight: 900;
  font-size: ${props => props.isCenter ? '1.4rem' : '1.2rem'};
  width: ${props => props.isCenter ? '44px' : '38px'};
  height: ${props => props.isCenter ? '44px' : '38px'};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 2;
  border: 2px solid rgba(255, 255, 255, 0.3);

  @media (max-width: 768px) {
    top: 12px;
    left: 12px;
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }

  @media (max-width: 480px) {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
`;

const RatingBadge = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  color: #f16925;
  font-weight: 600;
  font-size: ${props => props.isCenter ? '0.95rem' : '0.85rem'};
  padding: ${props => props.isCenter ? '8px 14px' : '6px 10px'};
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 2;
  border: 1px solid rgba(255, 255, 255, 0.3);

  @media (max-width: 768px) {
    top: 12px;
    right: 12px;
    font-size: 0.8rem;
    padding: 4px 8px;
  }
`;

const RestaurantLogoWrapper = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: ${props => props.isCenter ? '80px' : '70px'};
  height: ${props => props.isCenter ? '80px' : '70px'};
  background: linear-gradient(145deg, #ffffff 0%, #f8f8f8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.9);
  overflow: hidden;
  padding: 8px;
  z-index: 10;

  @media (max-width: 768px) {
    width: ${props => props.isCenter ? '70px' : '60px'};
    height: ${props => props.isCenter ? '70px' : '60px'};
    border: 2px solid rgba(255, 255, 255, 0.9);
    padding: 6px;
  }

  @media (max-width: 480px) {
    width: ${props => props.isCenter ? '60px' : '50px'};
    height: ${props => props.isCenter ? '60px' : '50px'};
    padding: 4px;
  }
`;

const BannerImage = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const RestaurantLogo = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const RestaurantLogoFallback = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f16925 0%, #e05a1a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => props.isCenter ? '2.2rem' : '1.8rem'};
  color: white;
  font-weight: bold;

  @media (max-width: 768px) {
    font-size: ${props => props.isCenter ? '2rem' : '1.5rem'};
  }

  @media (max-width: 480px) {
    font-size: ${props => props.isCenter ? '1.8rem' : '1.3rem'};
  }
`;

const RestaurantContent = styled.div`
  padding: ${props => props.isCenter ? '3.6rem 2.2rem 2.2rem' : '2.8rem 1.8rem 1.8rem'};
  display: flex;
  flex-direction: column;
  gap: ${props => props.isCenter ? '1.4rem' : '1rem'};
  flex: 1;
  text-align: center;
  position: relative;

  @media (max-width: 768px) {
    padding: ${props => props.isCenter ? '3.2rem 2rem 2rem' : '2.4rem 1.5rem 1.5rem'};
    gap: ${props => props.isCenter ? '1.2rem' : '0.8rem'};
  }

  @media (max-width: 480px) {
    padding: ${props => props.isCenter ? '2.8rem 1.5rem 1.5rem' : '2rem 1.2rem 1.2rem'};
    gap: ${props => props.isCenter ? '1rem' : '0.6rem'};
  }
`;

const RestaurantName = styled.h3`
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
  color: #222;
  line-height: 1.3;
  transition: color 0.3s ease;

  &:hover {
    color: #f16925;
  }

  @media (max-width: 768px) {
    font-size: 1.4rem;
  }
`;

const RestaurantCategory = styled.p`
  margin: 0;
  font-size: ${props => props.isCenter ? '1rem' : '0.9rem'};
  background: linear-gradient(135deg, #f16925 0%, #D8DF20 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1.5px;

  @media (max-width: 768px) {
    font-size: ${props => props.isCenter ? '0.95rem' : '0.85rem'};
    letter-spacing: 1px;
  }

  @media (max-width: 480px) {
    font-size: ${props => props.isCenter ? '0.9rem' : '0.8rem'};
    letter-spacing: 0.5px;
  }
`;

const LocationInfo = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  /* Increased contrast from #777 to #555 for accessibility */
  color: #555;
  font-size: ${props => props.isCenter ? '0.9rem' : '0.8rem'};
  margin: 0.5rem 0;

  @media (max-width: 768px) {
    font-size: ${props => props.isCenter ? '0.85rem' : '0.75rem'};
  }
`;

const RestaurantDescription = styled.p`
  margin: 0;
  font-size: ${props => props.isCenter ? '1rem' : '0.9rem'};
  /* Slightly darker for accessibility */
  color: #555;
  line-height: 1.6;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: ${props => props.isCenter ? 3 : 2};
  -webkit-box-orient: vertical;
  overflow: hidden;
  opacity: 0.8;

  @media (max-width: 768px) {
    font-size: ${props => props.isCenter ? '0.95rem' : '0.85rem'};
    line-height: 1.5;
    -webkit-line-clamp: 2;
  }

  @media (max-width: 480px) {
    font-size: ${props => props.isCenter ? '0.9rem' : '0.8rem'};
  }
`;

const ViewButton = styled(Link)`
  /* Use brand orange background with white text for high contrast */
  background: #f16925;
  border: 2px solid #f16925;
  color: #ffffff;
  padding: 0.6rem 1.2rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;

  &:hover {
    /* Darker shade on hover while keeping contrast */
    background: #d85f21;
    border-color: #d85f21;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(216, 95, 33, 0.25);
  }

  @media (max-width: 768px) {
    padding: 0.7rem 1.5rem;
    font-size: 0.8rem;
    border-radius: 10px;

    &:hover {
      transform: translateY(-2px);
    }
  }

  @media (max-width: 480px) {
    padding: 0.6rem 1.2rem;
    font-size: 0.75rem;
    border-radius: 8px;
  }
`;

const DotsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 3rem;

  @media (max-width: 768px) {
    margin-top: 2.5rem;
    gap: 10px;
  }
`;

const Dot = styled.button`
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: none;
  background: ${props => props.$active 
    ? 'linear-gradient(135deg, #f16925 0%, #e05a1a 100%)' 
    : 'linear-gradient(135deg, #e0e0e0 0%, #c0c0c0 100%)'};
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: ${props => props.$active 
    ? '0 4px 12px rgba(241, 105, 37, 0.3)' 
    : '0 2px 6px rgba(0, 0, 0, 0.1)'};

  &:hover {
    background: ${props => props.$active 
      ? 'linear-gradient(135deg, #e05a1a 0%, #d04516 100%)' 
      : 'linear-gradient(135deg, #c0c0c0 0%, #a0a0a0 100%)'};
    transform: scale(1.2);
  }

  @media (max-width: 768px) {
    width: 12px;
    height: 12px;
  }

  @media (max-width: 480px) {
    width: 10px;
    height: 10px;
  }
`;

const SwipeHint = styled.div`
  display: none;
  text-align: center;
  margin-top: 1.5rem;
  /* Increased contrast from #999 to #666 for readability */
  color: #666;
  font-size: 0.85rem;
  font-style: italic;
  opacity: 0.8;

  @media (max-width: 768px) {
    display: block;
  }
`;

const Top10Stands = () => {
  const { top10Stands, loading, error, fetchTop10Stands } = useTop10Store();
  const { language } = useLanguageStore();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [imageErrors, setImageErrors] = useState({});
  const [imageLoaded, setImageLoaded] = useState({});
  const [isPaused, setIsPaused] = useState(false);
  const [isMobile, setIsMobile] = useState(() => typeof window !== 'undefined' ? window.innerWidth <= 768 : false);
  const intervalRef = useRef(null);

  const content = {
    title: language === 'es' ? 'Las Estrellas de Mercado' : 'The Stars of Mercado',
    subtitle: language === 'es' ? 'Descubre los 10 restaurantes más destacados con las mejores experiencias gastronómicas' : 'Discover our top 10 featured restaurants with the finest dining experiences',
    viewButton: language === 'es' ? 'Explorar' : 'Explore',
    swipeHint: language === 'es' ? 'Desliza para descubrir más restaurantes' : 'Swipe to discover more restaurants',
    location: language === 'es' ? 'Centro Comercial' : 'Shopping Center'
  };

  useEffect(() => {
    fetchTop10Stands();
  }, [fetchTop10Stands]);

  // Auto-rotation effect
  useEffect(() => {
    if (top10Stands.length > 0 && !isPaused) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % top10Stands.length);
      }, 4000); // Change slide every 4 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [top10Stands.length, isPaused]);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % top10Stands.length);
    // Pause auto-rotation temporarily when user interacts
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 8000); // Resume after 8 seconds
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + top10Stands.length) % top10Stands.length);
    // Pause auto-rotation temporarily when user interacts
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 8000); // Resume after 8 seconds
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
    // Pause auto-rotation temporarily when user interacts
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 8000); // Resume after 8 seconds
  };

  const getRestaurantLogo = (name) => {
    return name.charAt(0).toUpperCase();
  };

  // Helper function to get the appropriate description based on current language
  const getLocalizedDescription = (stand) => {
    if (language === 'es') {
      return stand.description_es || stand.description_en || stand.description || '';
    } else {
      return stand.description_en || stand.description_es || stand.description || '';
    }
  };

  const handleImageError = (restaurantId, imageType) => {
    setImageErrors(prev => ({
      ...prev,
      [`${restaurantId}_${imageType}`]: true
    }));
  };

  const handleImageLoad = (restaurantId, imageType) => {
    setImageLoaded(prev => ({
      ...prev,
      [`${restaurantId}_${imageType}`]: true
    }));
  };

  const getCardPosition = (index) => {
    if (index === currentSlide) return 'center';
    if (index === (currentSlide - 1 + top10Stands.length) % top10Stands.length) return 'left';
    if (index === (currentSlide + 1) % top10Stands.length) return 'right';
    return 'hidden';
  };

  // Pause auto-rotation on hover
  const handleMouseEnter = () => {
    setIsPaused(true);
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
  };

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!top10Stands || top10Stands.length === 0) {
    return null;
  }

  return (
    <Section>
      <Container>
        <Header>
          <Title>{content.title}</Title>
          <Subtitle>{content.subtitle}</Subtitle>
        </Header>

        <CarouselContainer 
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <NavigationButton 
            onClick={prevSlide}
            aria-label={language === 'es' ? 'Anterior' : 'Previous'}
          >
            <ChevronLeft size={isMobile ? 22 : 26} />
          </NavigationButton>

          <CarouselWrapper>
            <CarouselTrack>
              {top10Stands.map((restaurant, index) => {
                const position = getCardPosition(index);
                const isCenter = position === 'center';
                const isVisible = ['center', 'left', 'right'].includes(position);

                if (!isVisible) return null;

                return (
                  <RestaurantCard 
                    key={restaurant.id}
                    isCenter={isCenter}
                    position={position}
                  >
                    <RestaurantBanner isCenter={isCenter}>
                      {restaurant.banner_url && !imageErrors[`${restaurant.id}_banner`] && (
                        <BannerImage
                          src={restaurant.banner_url}
                          alt={`${restaurant.name} banner`}
                          loading="lazy"
                          className={imageLoaded[`${restaurant.id}_banner`] ? 'loaded' : 'loading'}
                          onLoad={() => handleImageLoad(restaurant.id, 'banner')}
                          onError={() => handleImageError(restaurant.id, 'banner')}
                        />
                      )}
                      <RatingBadge isCenter={isCenter}>
                        <Star size={14} fill="currentColor" />
                        4.{Math.floor(Math.random() * 10)}
                      </RatingBadge>
                      <RestaurantLogoWrapper isCenter={isCenter}>
                        {restaurant.avatar && !imageErrors[`${restaurant.id}_avatar`] ? (
                          <RestaurantLogo 
                            src={restaurant.avatar} 
                            alt={restaurant.name}
                            loading="lazy"
                            className={imageLoaded[`${restaurant.id}_avatar`] ? 'loaded' : 'loading'}
                            onLoad={() => handleImageLoad(restaurant.id, 'avatar')}
                            onError={() => handleImageError(restaurant.id, 'avatar')}
                          />
                        ) : (
                          <RestaurantLogoFallback isCenter={isCenter}>
                            {getRestaurantLogo(restaurant.name)}
                          </RestaurantLogoFallback>
                        )}
                      </RestaurantLogoWrapper>
                    </RestaurantBanner>
                    
                    <RestaurantContent isCenter={isCenter}>
                      <RestaurantName isCenter={isCenter}>{restaurant.name}</RestaurantName>
                      <RestaurantCategory isCenter={isCenter}>{restaurant.category}</RestaurantCategory>
                      <LocationInfo isCenter={isCenter}>
                        <MapPin size={14} />
                        {content.location}
                      </LocationInfo>
                      <RestaurantDescription isCenter={isCenter}>
                        {getLocalizedDescription(restaurant)}
                      </RestaurantDescription>
                      <ViewButton to={`/stands-virtuales/${restaurant.slug}`} isCenter={isCenter}>
                        {content.viewButton}
                      </ViewButton>
                    </RestaurantContent>
                  </RestaurantCard>
                );
              })}
            </CarouselTrack>
          </CarouselWrapper>

          <NavigationButton 
            onClick={nextSlide}
            aria-label={language === 'es' ? 'Siguiente' : 'Next'}
          >
            <ChevronRight size={isMobile ? 22 : 26} />
          </NavigationButton>
        </CarouselContainer>

        <DotsContainer>
          {top10Stands.map((_, index) => (
            <Dot
              key={index}
              $active={index === currentSlide}
              onClick={() => goToSlide(index)}
              aria-label={`${language === 'es' ? 'Ir a la diapositiva' : 'Go to slide'} ${index + 1}`}
            />
          ))}
        </DotsContainer>

        <SwipeHint>{content.swipeHint}</SwipeHint>
      </Container>
    </Section>
  );
};

export default Top10Stands; 