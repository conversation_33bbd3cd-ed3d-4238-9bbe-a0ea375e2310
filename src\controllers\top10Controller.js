const pool = require('../db');

const getTop10Stands = async (req, res) => {
  try {
    const query = `
      SELECT s.* 
      FROM stands s
      INNER JOIN top_10 t ON s.id = t.id_local
      ORDER BY t.number ASC
      LIMIT 10
    `;
    
    const result = await pool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error al obtener top 10 stands:', error);
    res.status(500).json({ error: 'Error al obtener los stands más populares' });
  }
};

module.exports = {
  getTop10Stands
}; 