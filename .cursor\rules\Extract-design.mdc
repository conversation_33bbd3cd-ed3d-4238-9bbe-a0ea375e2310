---
description: 
globs: 
alwaysApply: true
---
ANALYZE: the image pasted
OBJ<PERSON>TIVE
Extract a comprehensive and reusable design system from the image pasted, excluding any specific visual content, to create a JSON reference that developers or AI systems can use as a styling foundation for consistent UI development.
INSTRUCTIONS


Examine the image pasted to identify:
• Color schemes and palettes
• Font hierarchies and typography standards
• Spacing and margin conventions
• Structural layouts (grid systems, card designs, wrapper elements, etc.)
• Interactive elements (button styles, form inputs, data tables, etc.)
• Visual effects (corner rounding, drop shadows, and additional styling treatments)
Generate a design-system.json file that systematically documents these design principles and enables consistent visual language reproduction.
Save the JSON output to the designs directory using filename: design.json


REQUIREMENTS


Prioritize extracting scalable design tokens over copying specific interface content
Structure JSON with clear hierarchy and developer-accessible formatting
Include complete color systems, font scaling, and dimensional standards

Record component variations and interaction states where visible
Follow contemporary design system best practices
Omit any literal text, imagery, or data from source screenshots
Create framework-independent output suitable for any implementation environment

