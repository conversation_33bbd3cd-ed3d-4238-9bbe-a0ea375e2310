// src/pages/VerifyEmail.jsx
import styled from 'styled-components';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #f7f7f7;
  padding: 2rem;
  text-align: center;
`;

const Box = styled.div`
  max-width: 500px;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
`;

const Title = styled.h1`
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
`;

const Text = styled.p`
  font-size: 1rem;
  color: #555;
`;

export default function VerifyEmail() {
  return (
    <Container>
      <Box>
        <Title>¡Casi listo!</Title>
        <Text>
          Te hemos enviado un correo para verificar tu cuenta.
          <br />
          Por favor revisa tu bandeja de entrada (y tu carpeta de spam).
        </Text>
        <Text>
          Una vez confirmes tu correo, podrás iniciar sesión.
        </Text>
      </Box>
    </Container>
  );
}
