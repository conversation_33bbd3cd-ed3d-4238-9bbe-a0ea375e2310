import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUserStore } from '../store/useUserStore';
import { supabase } from '../utils/supabaseClient';
import Sidebar from '../components/dashboard/Sidebar';
import TopNavbar from '../components/dashboard/TopNavbar';
import MobileMenu from '../components/dashboard/MobileMenu';
import HomeDashboard from './dashboard/Home';
import EditStand from './dashboard/EditStand';
import Products from './dashboard/Products';
import AIStand from './dashboard/AIStand';
import Points from './dashboard/Points';
import PointsHistory from './dashboard/PointsHistory';
import Loader from '../components/Loader';

const PanelContainer = styled(motion.div)`
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
`;

const MainContent = styled(motion.main)`
  flex: 1;
  background: #f8f9fa;
  min-height: 100vh;
  margin-left: 15%;
  padding-top: 60px;

  @media (max-width: 1024px) {
    margin-left: 0;
    padding-top: 0;
    width: 100%;
  }
`;

const MyPanel = () => {
  const { role, userData, standId, standData, fetchUserData } = useUserStore();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🏪 MyPanel - Estado actual:', { 
      userData: userData ? { id: userData.id, role: userData.role } : null,
      role,
      standId,
      standData: standData ? { id: standData.id, name: standData.name } : null
    });

    if (userData?.id) {
      fetchUserData(userData.id);
    }
    setLoading(false);
  }, [userData?.id, fetchUserData]);

  // Debug para verificar el rol
  useEffect(() => {
    console.log('🔧 MyPanel - Verificando acceso:', { role, isLocal: role === 'local' });
  }, [role]);

  // Si el usuario no tiene rol de local, redirigir a la página principal
  if (role !== 'local') {
    console.log('❌ MyPanel - Acceso denegado. Rol requerido: local, rol actual:', role);
    return <Navigate to="/" replace />;
  }

  const handlePreview = () => {
    if (standData?.slug) {
      window.open(`/stands-virtuales/${standData.slug}`, '_blank');
    }
  };

  if (loading) {
    return <Loader />;
  }

  console.log('✅ MyPanel - Acceso permitido. Mostrando dashboard...');

  return (
    <PanelContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Sidebar />
      <TopNavbar 
        onPreview={handlePreview} 
        hasSlug={!!standData?.slug} 
      />
      <MobileMenu />
      <MainContent
        initial={{ x: 20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Routes>
          <Route path="/" element={<HomeDashboard />} />
          <Route path="/edit" element={<EditStand />} />
          <Route path="/products" element={<Products />} />
          <Route path="/ai" element={<AIStand />} />
          <Route path="/points" element={<Points />} />
          <Route path="/points-history" element={<PointsHistory />} />
        </Routes>
      </MainContent>
    </PanelContainer>
  );
};

export default MyPanel;