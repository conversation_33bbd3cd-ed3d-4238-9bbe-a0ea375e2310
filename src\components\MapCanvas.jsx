// src/components/MapCanvas.jsx
import React from 'react';
import Marker from 'react-image-marker';
import styled from 'styled-components';
const croquis = '/frame-map.webp';
import { Modal } from './ui/Modal';
import { FaMapMarkerAlt } from 'react-icons/fa';

const Wrapper = styled.div`
  flex: 1;
  position: relative;
  overflow: auto;
  height: 100%;

  @media (max-width: 768px) {
    height: 50vh;
  }
`;

const ModalContent = styled.div`
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
`;

const ModalTitle = styled.h2`
  color: #000;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
`;

const ModalText = styled.p`
  color: #333;
  margin-bottom: 1.5rem;
  line-height: 1.5;
`;

const ModalButton = styled.button`
  background: #D8DF20;
  color: #000;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1rem;

  &:hover {
    background: #e8ef30;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }
`;

const CustomMarker = styled.div`
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
`;

const MarkerIcon = styled.img`
  width: 32px;
  height: 32px;
  object-fit: contain;
`;

// Lista base de puntos en el mapa
const markerList = [
  { 
    id: 1, 
    top: 71, 
    left: 61.5, 
    content: 'Tapy Stand', 
    category: 'Stands', 
    description: 'Tapy Card stand de trajetas digitales y soluciones con tarjetas.', 
    virtualStandUrl: '/stands-virtuales/tapy',
    icon: '/icons/icon-tapy.webp'
  },
  { 
    id: 2, 
    top: 55, 
    left: 8, 
    content: 'Baños Zona A', 
    category: 'Baños', 
    description: 'Baños públicos ubicados en la zona A del evento.',
    icon: '/icons/toilet.webp'
  },
  { 
    id: 5, 
    top: 25, 
    left: 8, 
    content: 'Baños Zona B', 
    category: 'Baños', 
    description: 'Baños públicos ubicados en la zona B del evento.',
    icon: '/icons/toilet.webp'
  },
  { 
    id: 6, 
    top: 25, 
    left: 89, 
    content: 'Baños Zona C', 
    category: 'Baños', 
    description: 'Baños públicos ubicados en la zona C del evento.',
    icon: '/icons/toilet.webp'
  },
  { 
    id: 7, 
    top: 50, 
    left: 89, 
    content: 'Baños Zona D', 
    category: 'Baños', 
    description: 'Baños públicos ubicados en la zona D del evento.',
    icon: '/icons/toilet.webp'
  }
];

export const MapCanvas = ({ selectedMarker, setSelectedMarker, filterCategory, search }) => {
  // 🔧 Protección contra search undefined y filtrado por categoría
  const filteredMarkers = markerList
    .filter((m) => {
      const matchSearch = m.content.toLowerCase().includes((search || '').toLowerCase());
      const matchCategory = !filterCategory || m.category === filterCategory;
      return matchSearch && matchCategory;
    })
    .map(({ top, left, content }) => ({ top, left, content }));

  const handleMarkerClick = (content) => {
    const marker = markerList.find(m => m.content === content);
    setSelectedMarker(marker);
  };

  return (
    <Wrapper>
      <Marker
        src={croquis}
        markers={filteredMarkers}
        markerComponent={({ content }) => {
          const marker = markerList.find(m => m.content === content);
          return (
            <CustomMarker onClick={() => handleMarkerClick(content)} title={content}>
              {marker?.icon ? (
                <MarkerIcon src={marker.icon} alt={content} />
              ) : (
                <FaMapMarkerAlt size={24} color="#D8DF20" />
              )}
            </CustomMarker>
          );
        }}
      />

      <Modal $isOpen={!!selectedMarker} onClose={() => setSelectedMarker(null)}>
        <ModalContent>
          <ModalTitle>{selectedMarker?.content}</ModalTitle>
          <ModalText>{selectedMarker?.description}</ModalText>
          {selectedMarker?.virtualStandUrl && (
            <ModalButton onClick={() => window.location.href = selectedMarker.virtualStandUrl}>
              Ir al Stand Virtual
            </ModalButton>
          )}
        </ModalContent>
      </Modal>
    </Wrapper>
  );
};
