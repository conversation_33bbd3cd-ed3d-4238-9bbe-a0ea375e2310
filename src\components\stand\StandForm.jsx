import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  Store, Link as LinkIcon, Image, User, MessageSquare, 
  Tag, Mail, Phone, Facebook, Instagram, Linkedin, CreditCard
} from 'lucide-react';

const FormSection = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

const Header = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  padding-bottom: 1.5rem;
`;

const SectionTitle = styled.h2`
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

const FormGroup = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  width: 100%;
  align-items: center;
`;

const FullWidthGroup = styled(FormGroup)`
  grid-column: 1 / -1;
`;

const Label = styled.label`
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;

  svg {
    width: 16px;
    height: 16px;
    color: #6c757d;
  }
`;

const Input = styled.input`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  transition: all 0.2s;
  width: 100%;
  max-width: 100%;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  min-height: 100px;
  resize: vertical;
  transition: all 0.2s;
  width: 100%;
  max-width: 100%;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const Button = styled(motion.button)`
  background: #D8DF20;
  color: #000;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  align-self: flex-start;

  &:hover {
    background: #c4cb1c;
    transform: translateY(-1px);
  }

  &:disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  grid-column: 1 / -1;
`;

const StandForm = ({ formData = {}, handleChange, handleSubmit, loading }) => {
  // Asegurar que todos los campos tengan un valor por defecto
  const defaultFormData = {
    name: '',
    slug: '',
    banner_url: '',
    whatsapp: '',
    email: '',
    avatar: '',
    description: '',
    category: '',
    facebook: '',
    instagram: '',
    linkedin: '',
    'tarjeta-digital': '',
    ai_stands: ''
  };

  // Combinar los valores por defecto con los datos del formulario
  const safeFormData = { ...defaultFormData, ...formData };

  return (
    <FormSection>
      <Header>
        <Title>Editar Stand</Title>
      </Header>

      <Form onSubmit={handleSubmit}>
        <div>
          <SectionTitle>Información Básica</SectionTitle>
          <FormGroup>
            <Label>
              <Store />
              Nombre del Stand
            </Label>
            <Input
              type="text"
              name="name"
              value={safeFormData.name}
              onChange={handleChange}
              placeholder="Nombre del stand"
            />

            <Label>
              <LinkIcon />
              Slug (URL amigable)
            </Label>
            <Input
              type="text"
              name="slug"
              value={safeFormData.slug}
              onChange={handleChange}
              placeholder="URL amigable"
            />

            <Label>
              <Image />
              URL del Banner
            </Label>
            <Input
              type="url"
              name="banner_url"
              value={safeFormData.banner_url}
              onChange={handleChange}
              placeholder="URL del banner"
            />

            <Label>
              <User />
              URL del Avatar
            </Label>
            <Input
              type="url"
              name="avatar"
              value={safeFormData.avatar}
              onChange={handleChange}
              placeholder="URL del avatar"
            />

            <Label>
              <Tag />
              Categoría
            </Label>
            <Input
              type="text"
              name="category"
              value={safeFormData.category}
              onChange={handleChange}
              placeholder="Categoría del stand"
            />
          </FormGroup>

          <FullWidthGroup>
            <Label>
              <MessageSquare />
              Descripción
            </Label>
            <TextArea
              name="description"
              value={safeFormData.description}
              onChange={handleChange}
              placeholder="Descripción del stand"
            />
          </FullWidthGroup>
        </div>

        <div>
          <SectionTitle>Información de Contacto</SectionTitle>
          <FormGroup>
            <Label>
              <MessageSquare />
              WhatsApp
            </Label>
            <Input
              type="text"
              name="whatsapp"
              value={safeFormData.whatsapp}
              onChange={handleChange}
              placeholder="Número de WhatsApp"
            />

            <Label>
              <Mail />
              Email
            </Label>
            <Input
              type="email"
              name="email"
              value={safeFormData.email}
              onChange={handleChange}
              placeholder="Correo electrónico"
            />
          </FormGroup>
        </div>

        <div>
          <SectionTitle>Redes Sociales</SectionTitle>
          <FormGroup>
            <Label>
              <Facebook />
              Facebook
            </Label>
            <Input
              type="url"
              name="facebook"
              value={safeFormData.facebook}
              onChange={handleChange}
              placeholder="URL de Facebook"
            />

            <Label>
              <Instagram />
              Instagram
            </Label>
            <Input
              type="url"
              name="instagram"
              value={safeFormData.instagram}
              onChange={handleChange}
              placeholder="URL de Instagram"
            />

            <Label>
              <Linkedin />
              LinkedIn
            </Label>
            <Input
              type="url"
              name="linkedin"
              value={safeFormData.linkedin}
              onChange={handleChange}
              placeholder="URL de LinkedIn"
            />
          </FormGroup>
        </div>

        <div>
          <SectionTitle>Tarjeta Digital</SectionTitle>
          <FormGroup>
            <Label>
              <CreditCard />
              URL de la Tarjeta Digital
            </Label>
            <Input
              type="text"
              name="tarjeta-digital"
              value={safeFormData['tarjeta-digital']}
              onChange={handleChange}
              placeholder="URL de la tarjeta digital"
            />
          </FormGroup>
        </div>

        <ButtonGroup>
          <Button
            type="submit"
            disabled={loading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {loading ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </ButtonGroup>
      </Form>
    </FormSection>
  );
};

export default StandForm; 