import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, User, LogOut, Globe } from 'lucide-react';
import { useUserStore } from '../../store/useUserStore';
import { useAuthStore } from '../../store/useAuthStore';
import { useLanguageStore } from '../../store/useLanguageStore';

const NavbarContainer = styled.nav`
  position: fixed;
  top: 0;
  right: 0;
  left: 15%;
  height: 60px;
  background: #222222;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 2rem;
  z-index: 999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  @media (max-width: 1024px) {
    display: none;
  }
`;

const Logo = styled.img`
  width: 40px;
  height: 40px;
  object-fit: contain;
`;

const NavbarActions = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
`;

const PreviewButton = styled(motion.button)`
  background: #F16925;
  color: #fff;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: #e05a1a;
    transform: translateY(-2px);
  }

  &:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const UserMenu = styled.div`
  position: relative;
`;

const UserButton = styled(motion.button)`
  background: none;
  border: none;
  color: #fff;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(241, 105, 37, 0.1);
    color: #F16925;
  }

  svg {
    width: 20px;
    height: 20px;
    color: #F16925;
  }
`;

const DropdownMenu = styled(motion.div)`
  position: absolute;
  top: 100%;
  right: 0;
  background: #222222;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  overflow: hidden;
  margin-top: 0.5rem;
`;

const DropdownItem = styled(motion.a)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: rgba(241, 105, 37, 0.1);
    color: #F16925;
  }

  svg {
    width: 16px;
    height: 16px;
    color: #F16925;
  }
`;

const LanguageButton = styled(motion.button)`
  background: none;
  border: 2px solid #F16925;
  color: #F16925;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  min-width: 80px;
  justify-content: center;

  &:hover {
    background: #F16925;
    color: #fff;
    transform: translateY(-2px);
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const TopNavbar = ({ onPreview, hasSlug }) => {
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { userData } = useUserStore();
  const { logout } = useAuthStore();
  const { language, toggleLanguage } = useLanguageStore();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    }
  };

  return (
    <NavbarContainer>

      <NavbarActions>
        <LanguageButton
          onClick={toggleLanguage}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          title={language === 'es' ? 'Cambiar a inglés' : 'Switch to Spanish'}
        >
          <Globe size={16} />
          {language === 'es' ? 'EN' : 'ES'}
        </LanguageButton>

        {hasSlug && (
          <PreviewButton
            onClick={onPreview}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ExternalLink size={16} />
            {language === 'es' ? 'Ver Perfil' : 'View Profile'}
          </PreviewButton>
        )}

        <UserMenu>
          <UserButton
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <User size={20} />
            {userData?.name && userData?.lastname ? `${userData.name} ${userData.lastname}` : userData?.name || userData?.email}
          </UserButton>

          <AnimatePresence>
            {isDropdownOpen && (
              <DropdownMenu
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <DropdownItem
                  onClick={handleLogout}
                  whileHover={{ x: 5 }}
                >
                  <LogOut size={16} />
                  {language === 'es' ? 'Cerrar Sesión' : 'Logout'}
                </DropdownItem>
              </DropdownMenu>
            )}
          </AnimatePresence>
        </UserMenu>
      </NavbarActions>
    </NavbarContainer>
  );
};

export default TopNavbar; 