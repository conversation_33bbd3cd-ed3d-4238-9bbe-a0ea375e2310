import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '../utils/supabaseClient'

export const useAuthStore = create(persist(
  (set, get) => ({
    user: null,
    initialized: false,

    login: async (email, password) => {
      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) throw error;
        set({ user: data.user });
        return { user: data.user, error: null };
      } catch (error) {
        console.error('Error en login:', error);
        return { user: null, error: error.message };
      }
    },

    logout: async () => {
      try {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
        set({ user: null });
        window.location.href = '/';
        return { error: null };
      } catch (error) {
        return { error: error.message };
      }
    },

    // Password recovery methods
    requestPasswordReset: async (email) => {
      try {
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/reset-password`,
        });
        
        if (error) throw error;
        return { error: null };
      } catch (error) {
        console.error('Error en requestPasswordReset:', error);
        return { error: error.message };
      }
    },

    resetPassword: async (newPassword) => {
      try {
        const { data, error } = await supabase.auth.updateUser({
          password: newPassword
        });

        if (error) throw error;
        return { user: data.user, error: null };
      } catch (error) {
        console.error('Error en resetPassword:', error);
        return { user: null, error: error.message };
      }
    },

    setUser: (user) => set({ user }),

    checkUser: async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) throw error;
        set({ user: user || null, initialized: true });
        return { user: user || null, error: null };
      } catch (error) {
        console.error('Error en checkUser:', error);
        set({ initialized: true });
        return { user: null, error: error.message };
      }
    },

    // Initialize auth state listener
    initializeAuthListener: () => {
      if (get().initialized) return;

      supabase.auth.onAuthStateChange((event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        if (event === 'SIGNED_IN' && session?.user) {
          set({ user: session.user, initialized: true });
        } else if (event === 'SIGNED_OUT') {
          set({ user: null, initialized: true });
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          set({ user: session.user });
        }
      });

      set({ initialized: true });
    }
  }),
  {
    name: 'auth-storage',
    partialize: (state) => ({ user: state.user }),
  }
))
