// src/utils/itineraryData.js
export const itinerary = {
  day1: [
    {
      time: "11:00 AM",
      title: "Apertura de Puertas",
      description: "Bienvenida a EMEFEST",
      type: "general"
    },
    {
      time: "12:00 PM",
      title: "Inauguración del Evento",
      description: "Ceremonia de apertura oficial",
      type: "general"
    },
    {
      time: "1:20 PM - 1:50 PM",
      title: "Meet & Greet C1",
      description: "Encuentro con el conferencista 1",
      type: "meet"
    },
    {
      time: "1:50 PM",
      title: "Conferencia #1",
      description: "Primera conferencia del día",
      type: "conference"
    },
    {
      time: "1:40 PM",
      title: "Meet & Greet C2",
      description: "Encuentro con el conferencista 2",
      type: "meet"
    },
    {
      time: "1:50 PM - 2:05 PM",
      title: "Preguntas y Respuestas C1",
      description: "Sesión de preguntas para el conferencista 1",
      type: "qa"
    },
    {
      time: "2:10 PM",
      title: "Conferencia #2",
      description: "Segunda conferencia del día",
      type: "conference"
    },
    {
      time: "3:00 PM",
      title: "Preguntas y Respuestas C2",
      description: "Sesión de preguntas para el conferencista 2",
      type: "qa"
    },
    {
      time: "3:00 PM - 3:30 PM",
      title: "Tiempo de Descanso",
      description: "Pausa para networking",
      type: "break"
    },
    {
      time: "3:00 PM",
      title: "Meet & Greet C3",
      description: "Encuentro con el conferencista 3",
      type: "meet"
    },
    {
      time: "3:30 PM",
      title: "Conferencia #3",
      description: "Tercera conferencia del día",
      type: "conference"
    },
    {
      time: "4:00 PM",
      title: "Meet & Greet C4",
      description: "Encuentro con el conferencista 4",
      type: "meet"
    },
    {
      time: "4:20 PM",
      title: "Preguntas y Respuestas C3",
      description: "Sesión de preguntas para el conferencista 3",
      type: "qa"
    },
    {
      time: "4:25 PM",
      title: "Conferencia #4",
      description: "Cuarta conferencia del día",
      type: "conference"
    },
    {
      time: "5:15 PM",
      title: "Preguntas y Respuestas C4",
      description: "Sesión de preguntas para el conferencista 4",
      type: "qa"
    },
    {
      time: "5:45 PM",
      title: "Networking",
      description: "Tiempo para networking y descanso",
      type: "break"
    },
    {
      time: "6:30 PM",
      title: "Taller Interactivo",
      description: "Taller práctico con tema asignado",
      type: "workshop"
    },
    {
      time: "7:30 PM",
      title: "Meet & Greet C5",
      description: "Encuentro con el conferencista 5",
      type: "meet"
    },
    {
      time: "8:00 PM",
      title: "Conferencia #5",
      description: "Quinta conferencia del día",
      type: "conference"
    },
    {
      time: "8:50 PM",
      title: "Preguntas y Respuestas C5",
      description: "Sesión de preguntas para el conferencista 5",
      type: "qa"
    },
    {
      time: "9:10 PM",
      title: "Cierre del Evento",
      description: "Ceremonia de cierre del primer día",
      type: "general"
    },
    {
      time: "10:00 PM",
      title: "Cierre de Puertas",
      description: "Fin de actividades del día",
      type: "general"
    }
  ],
  day2: [
    {
      time: "11:00 AM",
      title: "Apertura de Puertas",
      description: "Bienvenida al segundo día de EMEFEST",
      type: "general"
    },
    {
      time: "12:00 PM",
      title: "Inauguración del Evento",
      description: "Ceremonia de apertura del segundo día",
      type: "general"
    },
    {
      time: "1:20 PM - 1:50 PM",
      title: "Meet & Greet C6",
      description: "Encuentro con el conferencista 6",
      type: "meet"
    },
    {
      time: "1:50 PM",
      title: "Conferencia #6",
      description: "Primera conferencia del día",
      type: "conference"
    },
    {
      time: "1:40 PM",
      title: "Meet & Greet C7",
      description: "Encuentro con el conferencista 7",
      type: "meet"
    },
    {
      time: "1:50 PM - 2:05 PM",
      title: "Preguntas y Respuestas C6",
      description: "Sesión de preguntas para el conferencista 6",
      type: "qa"
    },
    {
      time: "2:10 PM",
      title: "Conferencia #7",
      description: "Segunda conferencia del día",
      type: "conference"
    },
    {
      time: "3:00 PM",
      title: "Preguntas y Respuestas C7",
      description: "Sesión de preguntas para el conferencista 7",
      type: "qa"
    },
    {
      time: "3:00 PM - 3:30 PM",
      title: "Tiempo de Descanso",
      description: "Pausa para networking",
      type: "break"
    },
    {
      time: "3:00 PM",
      title: "Meet & Greet C8",
      description: "Encuentro con el conferencista 8",
      type: "meet"
    },
    {
      time: "3:30 PM",
      title: "Conferencia #8",
      description: "Tercera conferencia del día",
      type: "conference"
    },
    {
      time: "4:00 PM",
      title: "Meet & Greet C9",
      description: "Encuentro con el conferencista 9",
      type: "meet"
    },
    {
      time: "4:20 PM",
      title: "Preguntas y Respuestas C8",
      description: "Sesión de preguntas para el conferencista 8",
      type: "qa"
    },
    {
      time: "4:25 PM",
      title: "Conferencia #9",
      description: "Cuarta conferencia del día",
      type: "conference"
    },
    {
      time: "5:15 PM",
      title: "Preguntas y Respuestas C9",
      description: "Sesión de preguntas para el conferencista 9",
      type: "qa"
    },
    {
      time: "5:45 PM",
      title: "Networking",
      description: "Tiempo para networking y descanso",
      type: "break"
    },
    {
      time: "6:30 PM",
      title: "Taller Interactivo",
      description: "Taller práctico con tema asignado",
      type: "workshop"
    },
    {
      time: "7:30 PM",
      title: "Meet & Greet C10",
      description: "Encuentro con el conferencista 10",
      type: "meet"
    },
    {
      time: "8:00 PM",
      title: "Conferencia #10",
      description: "Quinta conferencia del día",
      type: "conference"
    },
    {
      time: "8:50 PM",
      title: "Preguntas y Respuestas C10",
      description: "Sesión de preguntas para el conferencista 10",
      type: "qa"
    },
    {
      time: "9:10 PM",
      title: "Cierre del Evento",
      description: "Ceremonia de cierre del segundo día",
      type: "general"
    },
    {
      time: "10:00 PM",
      title: "Cierre de Puertas",
      description: "Fin de actividades del día",
      type: "general"
    }
  ],
  day3: [
    {
      time: "11:00 AM",
      title: "Apertura de Puertas",
      description: "Bienvenida al tercer día de EMEFEST",
      type: "general"
    },
    {
      time: "12:00 PM",
      title: "Inauguración del Evento",
      description: "Ceremonia de apertura del tercer día",
      type: "general"
    },
    {
      time: "1:20 PM - 1:50 PM",
      title: "Meet & Greet C11",
      description: "Encuentro con el conferencista 11",
      type: "meet"
    },
    {
      time: "1:50 PM",
      title: "Conferencia #11",
      description: "Primera conferencia del día",
      type: "conference"
    },
    {
      time: "1:40 PM",
      title: "Meet & Greet C12",
      description: "Encuentro con el conferencista 12",
      type: "meet"
    },
    {
      time: "1:50 PM - 2:05 PM",
      title: "Preguntas y Respuestas C11",
      description: "Sesión de preguntas para el conferencista 11",
      type: "qa"
    },
    {
      time: "2:10 PM",
      title: "Conferencia #12",
      description: "Segunda conferencia del día",
      type: "conference"
    },
    {
      time: "3:00 PM",
      title: "Preguntas y Respuestas C12",
      description: "Sesión de preguntas para el conferencista 12",
      type: "qa"
    },
    {
      time: "3:00 PM - 3:30 PM",
      title: "Tiempo de Descanso",
      description: "Pausa para networking",
      type: "break"
    },
    {
      time: "3:00 PM",
      title: "Meet & Greet C13",
      description: "Encuentro con el conferencista 13",
      type: "meet"
    },
    {
      time: "3:30 PM",
      title: "Conferencia #13",
      description: "Tercera conferencia del día",
      type: "conference"
    },
    {
      time: "4:00 PM",
      title: "Meet & Greet C14",
      description: "Encuentro con el conferencista 14",
      type: "meet"
    },
    {
      time: "4:20 PM",
      title: "Preguntas y Respuestas C13",
      description: "Sesión de preguntas para el conferencista 13",
      type: "qa"
    },
    {
      time: "4:25 PM",
      title: "Conferencia #14",
      description: "Cuarta conferencia del día",
      type: "conference"
    },
    {
      time: "5:15 PM",
      title: "Preguntas y Respuestas C14",
      description: "Sesión de preguntas para el conferencista 14",
      type: "qa"
    },
    {
      time: "5:45 PM",
      title: "Networking",
      description: "Tiempo para networking y descanso",
      type: "break"
    },
    {
      time: "6:30 PM",
      title: "Taller Interactivo",
      description: "Taller práctico con tema asignado",
      type: "workshop"
    },
    {
      time: "7:30 PM",
      title: "Meet & Greet C15",
      description: "Encuentro con el conferencista 15",
      type: "meet"
    },
    {
      time: "8:00 PM",
      title: "Conferencia #15",
      description: "Quinta conferencia del día",
      type: "conference"
    },
    {
      time: "8:50 PM",
      title: "Preguntas y Respuestas C15",
      description: "Sesión de preguntas para el conferencista 15",
      type: "qa"
    },
    {
      time: "9:10 PM",
      title: "Cierre del Evento",
      description: "Ceremonia de cierre del tercer día",
      type: "general"
    },
    {
      time: "10:00 PM",
      title: "Cierre de Puertas",
      description: "Fin de actividades del día",
      type: "general"
    }
  ]
};
