import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Search } from 'lucide-react';
import { supabase } from '../utils/supabaseClient';
import { Link } from 'react-router-dom';
import { Footer } from '../components/Footer';
import { useLanguageStore } from '../store/useLanguageStore';

const PageWrapper = styled.div`
  background-color: #f4f4f9;
  min-height: 100vh;
`;

const BannerImage = styled.div`
  width: 100%;
  height: 260px;
  background-color: #2c3e50;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 0.5rem;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/people/1Q1A3269.webp');
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease-in;
    z-index: 0;
  }

  &.image-loaded::after {
    opacity: 1;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.45);
    z-index: 1;
  }
`;

const BannerTitle = styled.h1`
  color: #fff;
  font-size: 5rem;
  font-family: 'Bebas Neue', 'Arial Black', Arial, sans-serif;
  font-weight: 900;
  letter-spacing: 3px;
  z-index: 2;
  position: relative;
  text-align: center;
  margin: 0;
`;

const BannerWrapper = styled.div`
  width: 100%;
  padding: 3rem 0 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const SearchBox = styled.form`
  display: flex;
  align-items: center;
  background: white;
  border: 1.5px solid #F16925;
  border-radius: 16px;
  padding: 1rem 1.2rem;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  gap: 1rem;

  @media (max-width: 600px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1.2rem 0.7rem;
    border-radius: 18px;
  }
`;

const SearchIconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #F16925;
  font-size: 1.5rem;
  @media (max-width: 600px) {
    font-size: 1.7rem;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  border: none;
  font-size: 1.15rem;
  outline: none;
  background: transparent;
  padding: 0.7rem 0.9rem;
  border-radius: 10px;

  @media (max-width: 600px) {
    width: 100%;
    font-size: 1.05rem;
    padding: 0.9rem 0.7rem;
    border: 1px solid #eee;
    margin-left: 0;
  }
`;

const SearchButton = styled.button`
  background: #F16925;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 0.9rem 1.5rem;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
  &:hover {
    background: #e05a1a;
  }

  @media (max-width: 600px) {
    width: 100%;
    margin-left: 0;
    justify-content: center;
    padding: 1.1rem;
    font-size: 1.1rem;
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const Card = styled(Link)`
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s, transform 0.2s;
  text-decoration: none;
  color: inherit;
  &:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.13);
    transform: translateY(-4px);
  }
`;

const CardContent = styled.div`
  padding: 1.2rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const CardTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  color: #222;
`;

const CardDate = styled.div`
  color: #F16925;
  font-weight: 600;
  font-size: 1rem;
`;

const CardCategory = styled.span`
  background: #F16925;
  color: #fff;
  border-radius: 12px;
  font-size: 0.85rem;
  padding: 0.2rem 0.7rem;
  margin-right: 0.5rem;
`;

const CardLocation = styled.div`
  color: #666;
  font-size: 0.95rem;
`;

const CardLink = styled(Link)`
  margin-top: 0.7rem;
  color: #F16925;
  font-weight: 600;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
`;

const Events = () => {
  const [search, setSearch] = useState('');
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const { language } = useLanguageStore();
  const bannerRef = useRef(null);

  const content = {
    title: language === 'es' ? 'Eventos' : 'Events',
    searchPlaceholder: language === 'es' ? 'Buscar eventos...' : 'Search events...',
    searchButton: language === 'es' ? 'Buscar evento' : 'Search event',
    loading: language === 'es' ? 'Cargando eventos...' : 'Loading events...',
    viewMore: language === 'es' ? 'Ver más' : 'View more',
    noImage: language === 'es' ? 'Sin imagen disponible' : 'No image available',
    categories: {
      'Food': language === 'es' ? 'Comida' : 'Food',
      'Entertainment': language === 'es' ? 'Entretenimiento' : 'Entertainment',
      'Music': language === 'es' ? 'Música' : 'Music',
      'Art': language === 'es' ? 'Arte' : 'Art',
      'Sports': language === 'es' ? 'Deportes' : 'Sports',
      'Business': language === 'es' ? 'Negocios' : 'Business',
      'Other': language === 'es' ? 'Otros' : 'Other'
    }
  };

  useEffect(() => {
    fetchEvents();

    const img = new Image();
    img.src = '/people/1Q1A3269.webp';
    img.onload = () => {
      if (bannerRef.current) {
        bannerRef.current.classList.add('image-loaded');
      }
    };
  }, []);

  // Helper function to get the appropriate text based on current language
  const getLocalizedText = (event, field) => {
    const esField = `${field}_es`;
    const enField = `${field}_en`;
    const fallbackField = field;

    if (language === 'es') {
      return event[esField] || event[enField] || event[fallbackField] || '';
    } else {
      return event[enField] || event[esField] || event[fallbackField] || '';
    }
  };

  const fetchEvents = async () => {
    setLoading(true);
    let { data, error } = await supabase
      .from('events')
      .select('*')
      .order('start_date', { ascending: true });
    if (!error) setEvents(data);
    setLoading(false);
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    // Search in both language fields
    let { data, error } = await supabase
      .from('events')
      .select('*')
      .or(`title.ilike.%${search}%,title_es.ilike.%${search}%,title_en.ilike.%${search}%,description.ilike.%${search}%,description_es.ilike.%${search}%,description_en.ilike.%${search}%`);
    
    if (!error) setEvents(data);
    setLoading(false);
  };

  // Función para traducir la categoría
  const translateCategory = (event) => {
    const category = getLocalizedText(event, 'category');
    return content.categories[category] || category;
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <BannerImage ref={bannerRef}>
          <BannerTitle>{content.title}</BannerTitle>
        </BannerImage>
        <BannerWrapper>
          <SearchBox onSubmit={handleSearch}>
            <SearchIconWrapper><Search size={22} /></SearchIconWrapper>
            <SearchInput
              type="text"
              placeholder={content.searchPlaceholder}
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <SearchButton type="submit">
              {content.searchButton}
            </SearchButton>
          </SearchBox>
        </BannerWrapper>
        {loading ? (
          <div style={{textAlign:'center', marginTop:'3rem'}}>{content.loading}</div>
        ) : (
          <Grid>
            {events.map(event => (
              <Card key={event.id} to={`/eventinfo/${event.id}`}>
                {event.image_url ? (
                  <img src={event.image_url} alt={getLocalizedText(event, 'title')} loading="lazy" style={{width: '100%', height: '180px', objectFit: 'cover'}} />
                ) : (
                  <div style={{width: '100%', height: '180px', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#666'}}>
                    {content.noImage}
                  </div>
                )}
                <CardContent>
                  <CardCategory>{translateCategory(event)}</CardCategory>
                  <CardTitle>{getLocalizedText(event, 'title')}</CardTitle>
                  <CardDate>
                    {event.start_date} {event.start_time ? `| ${event.start_time}` : ''}
                  </CardDate>
                  <CardLocation>{getLocalizedText(event, 'location')}</CardLocation>
                  <CardLink to={`/eventinfo/${event.id}`}>
                    {content.viewMore}
                  </CardLink>
                </CardContent>
              </Card>
            ))}
          </Grid>
        )}
        <Footer />
      </PageWrapper>
    </>
  );
};

export default Events; 