import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

export const useStandsStore = create((set) => ({
  stands: [],
  loading: false,
  error: null,
  fetchStands: async () => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('stands')
        .select(`
          id,
          name,
          banner_url,
          avatar,
          slug,
          category,
          description,
          unit,
          unit_type,
          phone_default
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      set({ stands: data || [], loading: false });
    } catch (err) {
      console.error('Error fetching stands:', err);
      set({ error: 'Error al cargar los stands', loading: false });
    }
  },
}));
