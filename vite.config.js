import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  
  // Build optimizations
  build: {
    // Generate sourcemaps for debugging
    sourcemap: false,
    
    // Optimize chunk splitting
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'ui-vendor': ['react-hot-toast', 'framer-motion'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'chart-vendor': ['chart.js', 'react-chartjs-2'],
          'icons-vendor': ['react-icons', 'lucide-react'],
          
          // Feature chunks
          'auth-pages': [
            './src/pages/Login.jsx',
            './src/pages/Register.jsx',
            './src/pages/ForgotPassword.jsx',
            './src/pages/ResetPassword.jsx',
            './src/pages/VerifyEmail.jsx'
          ],
          'dashboard-pages': [
            './src/pages/MyPanel.jsx',
            './src/pages/dashboard/Home.jsx',
            './src/pages/dashboard/Products.jsx',
            './src/pages/dashboard/Points.jsx'
          ],
          'event-pages': [
            './src/pages/Events.jsx',
            './src/pages/EventDetails.jsx',
            './src/pages/Itinerary.jsx'
          ]
        }
      }
    },
    
    // Compression and minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
      },
    },
    
    // Asset handling
    assetsInlineLimit: 4096, // Inline small assets
    chunkSizeWarningLimit: 1000, // Warn for chunks larger than 1MB
  },
  
  // Development server optimizations
  server: {
    fs: {
      strict: false,
    },
  },
  
  // Dependency optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      'react-hot-toast',
      'zustand'
    ],
    exclude: [
      // Exclude heavy libraries that should be loaded on demand
      'chart.js',
      'react-chartjs-2',
      'framer-motion'
    ]
  },
  
  // Performance optimizations
  esbuild: {
    // Remove console.log in production
    drop: ['console', 'debugger'],
  },
})
