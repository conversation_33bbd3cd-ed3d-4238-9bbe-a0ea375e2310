import React from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import Hero from '../components/Hero';
import { Gift, Star, Award } from 'lucide-react';

const PageWrapper = styled.div`
  padding: 2rem;
  background-color: #f4f4f9;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 1rem;
  }

  @media (max-width: 480px) {
    padding: 0.5rem;
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    margin-bottom: 2rem;
  }

  @media (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
    gap: 0.8rem;
  }

  @media (max-width: 480px) {
    font-size: 1.5rem;
    gap: 0.5rem;
    flex-direction: column;
  }

  svg {
    width: 40px;
    height: 40px;

    @media (max-width: 768px) {
      width: 32px;
      height: 32px;
    }

    @media (max-width: 480px) {
      width: 28px;
      height: 28px;
    }
  }
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 1.1rem;
    padding: 0 1rem;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
    padding: 0 0.5rem;
  }
`;

const RewardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem;
  }
`;

const RewardCard = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  @media (max-width: 480px) {
    border-radius: 8px;
  }
`;

const RewardImage = styled.div`
  width: 100%;
  height: 200px;
  background: ${({ color }) => color};
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    height: 180px;
  }

  @media (max-width: 480px) {
    height: 160px;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
  }
`;

const RewardIcon = styled.div`
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  z-index: 1;

  @media (max-width: 768px) {
    width: 70px;
    height: 70px;
  }

  @media (max-width: 480px) {
    width: 60px;
    height: 60px;
  }

  svg {
    width: 40px;
    height: 40px;
    color: ${({ color }) => color};

    @media (max-width: 768px) {
      width: 35px;
      height: 35px;
    }

    @media (max-width: 480px) {
      width: 30px;
      height: 30px;
    }
  }
`;

const RewardContent = styled.div`
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1.2rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
  }
`;

const RewardTitle = styled.h3`
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  color: #333;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
  }
`;

const RewardDescription = styled.p`
  margin: 0 0 1rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;

  @media (max-width: 480px) {
    font-size: 0.85rem;
    margin-bottom: 0.8rem;
  }
`;

const RewardPoints = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ff6f61;
  font-weight: bold;
  font-size: 1.1rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }

  @media (max-width: 480px) {
    font-size: 0.9rem;
  }

  svg {
    width: 20px;
    height: 20px;

    @media (max-width: 480px) {
      width: 18px;
      height: 18px;
    }
  }
`;

const mockRewards = [
  {
    id: 1,
    title: "Playera EME FEST",
    description: "Playera conmemorativa del evento con diseño exclusivo",
    points: 500,
    color: "#ff6f61",
    icon: <Gift size={24} />
  },
  {
    id: 2,
    title: "Gorra EME FEST",
    description: "Gorra con el logo del evento bordado",
    points: 400,
    color: "#4CAF50",
    icon: <Gift size={24} />
  },
  {
    id: 3,
    title: "Libreta EME FEST",
    description: "Libreta de notas con diseño personalizado",
    points: 300,
    color: "#2196F3",
    icon: <Gift size={24} />
  },
  {
    id: 4,
    title: "Tarjeta NFC Premium",
    description: "Tarjeta física NFC con acceso a contenido exclusivo",
    points: 1000,
    color: "#9C27B0",
    icon: <Award size={24} />
  },
  {
    id: 5,
    title: "Kit EME FEST",
    description: "Kit completo con playera, gorra y libreta",
    points: 1200,
    color: "#FFC107",
    icon: <Star size={24} />
  },
  {
    id: 6,
    title: "Tarjeta NFC Básica",
    description: "Tarjeta física NFC con acceso a contenido básico",
    points: 500,
    color: "#607D8B",
    icon: <Award size={24} />
  }
];

export const Recompensas = () => {
  return (
    <>
      <Navbar />
      <Hero />
      <PageWrapper>
        <Header>
          <Title>
            <Gift size={40} />
            Recompensas EME FEST
          </Title>
          <Subtitle>
            Canjea tus puntos por productos exclusivos del evento. 
            ¡Cuanto más participes, más puntos ganarás!
          </Subtitle>
        </Header>
        <RewardsGrid>
          {mockRewards.map((reward) => (
            <RewardCard key={reward.id}>
              <RewardImage color={reward.color}>
                <RewardIcon color={reward.color}>
                  {reward.icon}
                </RewardIcon>
              </RewardImage>
              <RewardContent>
                <RewardTitle>{reward.title}</RewardTitle>
                <RewardDescription>{reward.description}</RewardDescription>
                <RewardPoints>
                  <Star size={20} />
                  {reward.points} puntos
                </RewardPoints>
              </RewardContent>
            </RewardCard>
          ))}
        </RewardsGrid>
      </PageWrapper>
    </>
  );
}; 