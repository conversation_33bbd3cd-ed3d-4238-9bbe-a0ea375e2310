import React, { useState } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useLanguageStore } from '../store/useLanguageStore';
import useAnalytics from '../hooks/useAnalytics';

const PostCard = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const PostImage = styled.div`
  width: 100%;
  height: 200px;
  background-color: #f8f8f8;
  position: relative;
  overflow: hidden;
`;

const ProductImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const PostContent = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const PostTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  color: #333;
  font-weight: 600;
`;

const PostDescription = styled.p`
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
`;

const PostFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`;

const PostDate = styled.span`
  color: #999;
  font-size: 0.8rem;
`;

const PostPrice = styled.span`
  color: #F16925;
  font-size: 1.1rem;
  font-weight: 600;
  background: rgba(241, 105, 37, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
`;

const MenuBadge = styled.span`
  position: absolute;
  top: 1rem;
  right: 1rem;  background: #F16925;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
`;

const ExpandedPost = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow-y: auto;
`;

const ExpandedContent = styled.div`
  background: white;
  max-width: 800px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  z-index: 1001;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
`;

const ExpandedImage = styled.div`
  width: 100%;
  height: 400px;
  background-color: #f8f8f8;
  position: relative;
  overflow: hidden;
`;

const ExpandedProductImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const ExpandedText = styled.div`
  padding: 2rem;
`;

const ExpandedTitle = styled.h2`
  margin: 0 0 1rem;
  font-size: 2rem;
  color: #333;
`;

const ExpandedDescription = styled.p`
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
`;

const ExpandedDate = styled.span`
  display: block;
  margin-top: 1.5rem;
  color: #999;
  font-size: 0.9rem;
`;

const ProductCard = ({ post, onExpand }) => {
  const { language } = useLanguageStore();
  const { recordProductView } = useAnalytics();
  const [imageLoaded, setImageLoaded] = useState({});

  const handleClick = () => {
    recordProductView();
    onExpand(post);
  };

  const handleImageLoad = (imageType) => {
    setImageLoaded(prev => ({
      ...prev,
      [imageType]: true
    }));
  };

  // Helper function to get the appropriate text based on current language
  const getLocalizedText = (field) => {
    const esField = `${field}_es`;
    const enField = `${field}_en`;
    const fallbackField = field;

    if (language === 'es') {
      return post[esField] || post[enField] || post[fallbackField] || '';
    } else {
      return post[enField] || post[esField] || post[fallbackField] || '';
    }
  };

  const formatDate = (dateString) => {
    const locale = language === 'es' ? 'es-ES' : 'en-US';
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  const title = getLocalizedText('title');
  const description = getLocalizedText('description');
  const category = getLocalizedText('category');

  return (
    <>
      <PostCard onClick={handleClick}>
        <PostImage>
          {post.image_url && (
            <ProductImage
              src={post.image_url}
              alt={title}
              loading="lazy"
              className={imageLoaded.main ? 'loaded' : 'loading'}
              onLoad={() => handleImageLoad('main')}
            />
          )}
        </PostImage>
        {category && <MenuBadge>{category}</MenuBadge>}
        {post.category === 'producto' && <MenuBadge>Producto</MenuBadge>}
        {post.category === 'servicio' && <MenuBadge>Servicio</MenuBadge>}
        <PostContent>
          <PostTitle>{title}</PostTitle>
          <PostDescription>{description}</PostDescription>
          <PostFooter>
            <PostDate>{formatDate(post.created_at)}</PostDate>
            {post.precio && <PostPrice>${post.precio}</PostPrice>}
          </PostFooter>
        </PostContent>
      </PostCard>

      {post.isExpanded && (
        <ExpandedPost onClick={() => onExpand(null)}>
          <CloseButton onClick={(e) => {
            e.stopPropagation();
            onExpand(null);
          }}>
            <X size={24} />
          </CloseButton>
          <ExpandedContent onClick={(e) => e.stopPropagation()}>
            <ExpandedImage>
              {post.image_url && (
                <ExpandedProductImage
                  src={post.image_url}
                  alt={title}
                  loading="lazy"
                  className={imageLoaded.expanded ? 'loaded' : 'loading'}
                  onLoad={() => handleImageLoad('expanded')}
                />
              )}
            </ExpandedImage>
            <ExpandedText>
              <ExpandedTitle>{title}</ExpandedTitle>
              <ExpandedDescription>{description}</ExpandedDescription>
              <PostFooter style={{ marginTop: '1.5rem' }}>
                <ExpandedDate>{formatDate(post.created_at)}</ExpandedDate>
                {post.precio && <PostPrice>${post.precio}</PostPrice>}
              </PostFooter>
            </ExpandedText>
          </ExpandedContent>
        </ExpandedPost>
      )}
    </>
  );
};

export default ProductCard; 