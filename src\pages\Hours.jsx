import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Footer } from '../components/Footer';
import { useLanguageStore } from '../store/useLanguageStore';
import { FaCalendarAlt } from 'react-icons/fa';

const PageWrapper = styled.div`
  background: #f8f9fa;
  min-height: 100vh;
`;

const HeroContainer = styled.section`
  position: relative;
  width: 100%;
  height: 75vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0 3rem;
  background-color: #2c3e50;
  transition: background-color 0.5s ease;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/people/1Q1A3253.webp');
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease-in;
    z-index: 1;
  }

  &.image-loaded::after {
    opacity: 1;
  }

  & > * {
    position: relative;
    z-index: 2;
  }

  @media (max-width: 900px) {
    padding: 0 2rem;
    height: 60vh;
  }
  @media (max-width: 600px) {
    padding: 0 1rem;
    height: 50vh;
  }
`;

const CenteredTitle = styled.h1`
  position: relative;
  z-index: 3;
  color: #fff;
  font-family: 'Bebas Neue', sans-serif;
  font-size: 7rem;
  text-align: center;
  letter-spacing: 2px;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
  margin: 0;
  @media (max-width: 768px) {
    font-size: 4rem;
  }
  @media (max-width: 600px) {
    font-size: 3rem;
  }
`;

const HoursButton = styled.button`
  margin-top: 2.5rem;
  background: transparent;
  color: #fff;
  border: 2px solid #fff;
  border-radius: 50px;
  padding: 1rem 3rem;
  font-size: 1.5rem;
  font-family: 'Bebas Neue', sans-serif;
  font-weight: 400;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
  &:hover {
    background: #f16925;
    color: #fff;
    border-color: #f16925;
  }

  @media (max-width: 600px) {
    font-size: 1.2rem;
    padding: 0.8rem 2rem;
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const ContentSection = styled.div`
  padding: 4rem 1rem;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.05);
  margin-top: -80px;
  position: relative;
  z-index: 4;

  @media (max-width: 600px) {
    padding: 3rem 1rem;
    margin-top: -60px;
  }
`;

const HoursTable = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
`;

const HoursRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #f8f9fa;
    transform: translateX(5px);
  }
  
  @media (max-width: 600px) {
    padding: 1rem;
  }
  
  ${props => props.isToday && `
    background: #f16925;
    color: white;
    font-weight: 700;
    
    &:hover {
      background: #d35400;
    }
  `}
  
  ${props => props.isClosed && !props.isToday && `
    color: #a0aec0;
    
    &:hover {
      background: #f7fafc;
    }
  `}
`;

const DayName = styled.span`
  font-weight: 600;
  font-size: 1.1rem;
  color: #2d3748;
  
  @media (max-width: 600px) {
    font-size: 1rem;
  }
  
  ${props => props.isToday && `
    color: white;
  `}
`;

const HoursTime = styled.span`
  font-size: 1rem;
  font-weight: 500;
  color: #4a5568;
  
  @media (max-width: 600px) {
    font-size: 0.9rem;
  }
  
  ${props => props.isToday && `
    color: white;
  `}
`;

const DateDisplay = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f16925;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #fff;
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.07);
  border: 1px solid #f16925;

  @media (max-width: 600px) {
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
`;

const Hours = () => {
  const { language } = useLanguageStore();
  const [currentDate, setCurrentDate] = useState('');
  const [currentDay, setCurrentDay] = useState('');
  const hoursRef = useRef(null);
  const heroRef = useRef(null);

  useEffect(() => {
    const today = new Date();
    const dayNames = language === 'en' 
      ? ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      : ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    setCurrentDate(today.toLocaleDateString(language === 'en' ? 'en-US' : 'es-ES', options));
    setCurrentDay(dayNames[today.getDay()]);

    const img = new Image();
    img.src = '/people/1Q1A3253.webp';
    img.onload = () => {
      if (heroRef.current) {
        heroRef.current.classList.add('image-loaded');
      }
    };
  }, [language]);

  const scrollToHours = () => {
    hoursRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const schedule = language === 'en' ? [
    { day: 'Monday', hours: 'Closed' },
    { day: 'Tuesday', hours: '12:00 PM - 11:00 PM' },
    { day: 'Wednesday', hours: '12:00 PM - 11:00 PM' },
    { day: 'Thursday', hours: '12:00 PM - 11:00 PM' },
    { day: 'Friday', hours: '12:00 PM - 11:00 PM' },
    { day: 'Saturday', hours: '12:00 PM - 11:00 PM' },
    { day: 'Sunday', hours: '12:00 PM - 5:30 PM' }
  ] : [
    { day: 'Lunes', hours: 'Cerrado' },
    { day: 'Martes', hours: '12:00 PM - 11:00 PM' },
    { day: 'Miércoles', hours: '12:00 PM - 11:00 PM' },
    { day: 'Jueves', hours: '12:00 PM - 11:00 PM' },
    { day: 'Viernes', hours: '12:00 PM - 11:00 PM' },
    { day: 'Sábado', hours: '12:00 PM - 11:00 PM' },
    { day: 'Domingo', hours: '12:00 PM - 5:30 PM' }
  ];

  const content = {
    en: {
      title: 'Operating Hours',
      button: 'View Hours',
    },
    es: {
      title: 'Horarios de Operación',
      button: 'Ver Horarios',
    }
  };

  const t = content[language];
  
  return (
    <>
      <Navbar />
      <PageWrapper>
        <HeroContainer ref={heroRef}>
          <CenteredTitle>{t.title}</CenteredTitle>
          <HoursButton onClick={scrollToHours}>{t.button}</HoursButton>
        </HeroContainer>

        <Container>
          <ContentSection ref={hoursRef}>
            <DateDisplay>
              <FaCalendarAlt size={20} />
              <span>{currentDate}</span>
            </DateDisplay>
            <HoursTable>
              {schedule.map(item => (
                <HoursRow key={item.day} isToday={item.day === currentDay} isClosed={item.hours === 'Closed' || item.hours === 'Cerrado'}>
                  <DayName isToday={item.day === currentDay}>{item.day}</DayName>
                  <HoursTime isToday={item.day === currentDay}>{item.hours}</HoursTime>
                </HoursRow>
              ))}
            </HoursTable>
          </ContentSection>
        </Container>
        
        <Footer />
      </PageWrapper>
    </>
  );
};

export default Hours;