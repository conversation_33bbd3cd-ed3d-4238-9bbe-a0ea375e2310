// src/pages/Register.jsx
import { useState } from 'react'
import { <PERSON>, EyeOff, ArrowRight } from 'lucide-react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast';
import confetti from 'canvas-confetti';
import { supabase } from '../utils/supabaseClient';

// Estilos compartidos con login
const breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px'
}

const Container = styled.div`
  min-height: 100vh;
  background-image: url("/register.webp");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
`;

const Navbar = styled.nav`
  background-color: #F16925;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  width: 100%;
`;

const NavLogo = styled.img`
  height: 40px;
  object-fit: contain;

  @media (min-width: ${breakpoints.tablet}) {
    height: 50px;
  }
`;

const FormContainer = styled.div`
  width: 100%;
  max-width: 400px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @media (max-width: ${breakpoints.mobile}) {
    margin: 1rem auto;
    padding: 1rem;
    max-width: 90%;
  }

  @media (min-width: ${breakpoints.tablet}) {
    max-width: 500px;
    padding: 2.5rem;
  }

  @media (min-width: ${breakpoints.desktop}) {
    max-width: 600px;
    margin: 4rem auto;
  }
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: ${breakpoints.mobile}) {
    margin-bottom: 1.5rem;
  }
`;

const FormLogo = styled.img`
  height: 40px;
  margin-bottom: 1rem;

  @media (min-width: ${breakpoints.tablet}) {
    height: 40px;
  }

  @media (min-width: ${breakpoints.desktop}) {
    height: 50px;
  }
`;

const Title = styled.h1`
  font-size: 1.5rem;
  color: #333;
  margin: 0;

  @media (min-width: ${breakpoints.tablet}) {
    font-size: 2rem;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
`;

const FormGroup = styled.div`
  position: relative;
  width: 100%;
`;

const Input = styled.input`
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  background-color:#fff;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:focus {
    outline: none;
    border-color: #D8DF20;
  }
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 8px;
  display: flex;
  align-items: center;
  
  &:hover {
    color: #333;
  }
`;

const SubmitButton = styled.button`
  width: 100%;
  padding: 1rem;
  background-color: #2C2C2C;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
  margin-top: 1rem;

  @media (min-width: ${breakpoints.tablet}) {
    padding: 1.2rem;
    font-size: 1.1rem;
  }

  &:hover {
    background-color: #414141;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;


const Register = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [lastname, setLastname] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  const navigate = useNavigate()

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // 1. Registrar en auth.users
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            lastname
          }
        }
      });

      if (authError) throw authError;

      // 2. Insertar en public.users con rol "general"
      const { error: userError } = await supabase
        .from('users')
        .insert([
          {
            id: authData.user.id,
            name,
            lastname,
            email,
            role: 'general',
            points: 0,
            created_at: new Date().toISOString()
          }
        ]);

      if (userError) throw userError;

      // 🎉 Dispara el confetti
      confetti({
        particleCount: 150,
        spread: 90,
        origin: { y: 0.6 }
      });

      // ✅ Mensaje visual con toast
      toast.success('¡Registro exitoso! 🎉');

      // ⏳ Espera unos segundos antes de redirigir
      setTimeout(() => {
        navigate('/');
      }, 3000);

    } catch (error) {
      console.error('Error en el registro:', error);
      toast.error('Error al registrarte: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Navbar>
      <a href="/">
        <NavLogo src="/logo.webp" alt="Logo" loading="lazy" />
      </a>
      </Navbar>

      <FormContainer>
        <LoginHeader>
        
          <Title>Registrarme</Title>
        </LoginHeader>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Nombre"
              required
              disabled={loading}
            />
          </FormGroup>

          <FormGroup>
            <Input
              type="text"
              value={lastname}
              onChange={(e) => setLastname(e.target.value)}
              placeholder="Apellido"
              required
              disabled={loading}
            />
          </FormGroup>

          <FormGroup>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Correo electrónico"
              required
              disabled={loading}
            />
          </FormGroup>

          <FormGroup>
            <Input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Contraseña"
              required
              disabled={loading}
            />
            <PasswordToggle 
              type="button" 
              onClick={() => setShowPassword(!showPassword)}
              disabled={loading}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </PasswordToggle>
          </FormGroup>

          <SubmitButton type="submit" disabled={loading}>
            {loading ? 'Registrando...' : 'Registrarme'} <ArrowRight size={20} />
          </SubmitButton>
        </Form>
      </FormContainer>
    </Container>
  )
}

export default Register
