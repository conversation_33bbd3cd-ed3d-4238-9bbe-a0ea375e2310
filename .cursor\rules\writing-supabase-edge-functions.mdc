---
description: 
globs: 
alwaysApply: true
---
# Writing Supabase Edge Functions

You're a Supabase Edge Functions expert. Generate **high-quality Edge Functions** that adhere to the following best practices:

## General Guidelines

1. **Use TypeScript:**
   - All Edge Functions should be written in TypeScript
   - Leverage proper type definitions for better development experience

2. **Follow Deno Standards:**
   - Edge Functions run on Deno runtime
   - Use ES modules and modern JavaScript features
   - Import dependencies from reliable CDNs

3. **Handle CORS Properly:**
   - Always handle CORS for web applications
   - Return appropriate CORS headers

## File Structure

### Basic Edge Function Structure

```typescript
// supabase/functions/function-name/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Your function logic here
    const data = { message: 'Hello from Edge Function!' }
    
    return new Response(
      JSON.stringify(data),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 400,
      },
    )
  }
})
```

## Common Patterns

### Database Operations

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          full_name: string
        }
      }
    }
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role for full access
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get user from Authorization header
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    
    const { data: userData, error: authError } = await supabaseClient.auth.getUser(token)
    if (authError) throw authError

    // Perform database operations
    const { data, error } = await supabaseClient
      .from('profiles')
      .select('*')
      .eq('id', userData.user.id)
      .single()

    if (error) throw error

    return new Response(
      JSON.stringify({ data }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 400,
      },
    )
  }
})
```

### HTTP Method Handling

```typescript
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    switch (req.method) {
      case 'GET':
        return handleGet(req)
      case 'POST':
        return handlePost(req)
      case 'PUT':
        return handlePut(req)
      case 'DELETE':
        return handleDelete(req)
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 405,
          }
        )
    }
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 500,
      },
    )
  }
})

async function handleGet(req: Request) {
  // GET logic
  return new Response(JSON.stringify({ method: 'GET' }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handlePost(req: Request) {
  const body = await req.json()
  // POST logic
  return new Response(JSON.stringify({ method: 'POST', body }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}
```

### Request Body Validation

```typescript
import { z } from 'https://deno.land/x/zod@v3.21.4/mod.ts'

// Define validation schema
const CreateUserSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(20),
  full_name: z.string().min(1).max(100),
})

type CreateUserRequest = z.infer<typeof CreateUserSchema>

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (req.method !== 'POST') {
      throw new Error('Only POST method allowed')
    }

    // Parse and validate request body
    const body = await req.json()
    const validatedData: CreateUserRequest = CreateUserSchema.parse(body)

    // Use validated data
    console.log('Valid data:', validatedData)

    return new Response(
      JSON.stringify({ success: true, data: validatedData }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    )
  } catch (error) {
    let errorMessage = error.message
    let statusCode = 400

    if (error instanceof z.ZodError) {
      errorMessage = 'Validation failed'
      statusCode = 422
    }

    return new Response(
      JSON.stringify({ 
        error: errorMessage,
        details: error instanceof z.ZodError ? error.errors : undefined
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: statusCode,
      },
    )
  }
})
```

### External API Integration

```typescript
serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Example: Fetch data from external API
    const apiKey = Deno.env.get('EXTERNAL_API_KEY')
    if (!apiKey) {
      throw new Error('External API key not configured')
    }

    const response = await fetch('https://api.external-service.com/data', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`External API error: ${response.status}`)
    }

    const externalData = await response.json()

    // Process the data as needed
    const processedData = {
      timestamp: new Date().toISOString(),
      external: externalData,
      processed: true,
    }

    return new Response(
      JSON.stringify(processedData),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 500,
      },
    )
  }
})
```

### Webhook Handler

```typescript
import { createHmac } from 'https://deno.land/std@0.168.0/node/crypto.ts'

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (req.method !== 'POST') {
      throw new Error('Only POST method allowed')
    }

    // Verify webhook signature
    const signature = req.headers.get('x-webhook-signature')
    const body = await req.text()
    
    const webhookSecret = Deno.env.get('WEBHOOK_SECRET')
    if (!webhookSecret) {
      throw new Error('Webhook secret not configured')
    }

    const expectedSignature = createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex')

    if (signature !== `sha256=${expectedSignature}`) {
      throw new Error('Invalid signature')
    }

    // Process webhook payload
    const payload = JSON.parse(body)
    console.log('Webhook received:', payload)

    // Handle different webhook events
    switch (payload.type) {
      case 'user.created':
        await handleUserCreated(payload.data)
        break
      case 'user.updated':
        await handleUserUpdated(payload.data)
        break
      default:
        console.log('Unknown webhook type:', payload.type)
    }

    return new Response(
      JSON.stringify({ received: true }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 400,
      },
    )
  }
})

async function handleUserCreated(userData: any) {
  // Handle user creation webhook
  console.log('User created:', userData)
}

async function handleUserUpdated(userData: any) {
  // Handle user update webhook
  console.log('User updated:', userData)
}
```

## Best Practices

### Environment Variables

```typescript
// Always validate environment variables
const requiredEnvVars = {
  SUPABASE_URL: Deno.env.get('SUPABASE_URL'),
  SUPABASE_SERVICE_ROLE_KEY: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
}

for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (!value) {
    throw new Error(`Missing required environment variable: ${key}`)
  }
}
```

### Error Handling

```typescript
// Consistent error response format
function createErrorResponse(message: string, status: number = 400) {
  return new Response(
    JSON.stringify({ 
      error: message,
      timestamp: new Date().toISOString()
    }),
    {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
      status,
    },
  )
}

// Usage
if (!isValidInput) {
  return createErrorResponse('Invalid input provided', 422)
}
```

### Logging

```typescript
// Structured logging
function log(level: 'info' | 'error' | 'warn', message: string, extra?: any) {
  console.log(JSON.stringify({
    level,
    message,
    timestamp: new Date().toISOString(),
    ...extra,
  }))
}

// Usage
log('info', 'Function started', { method: req.method })
log('error', 'Database error', { error: error.message })
```

### Performance

```typescript
// Use streaming for large responses
async function streamData(data: any[]) {
  const stream = new ReadableStream({
    start(controller) {
      for (const item of data) {
        controller.enqueue(new TextEncoder().encode(JSON.stringify(item) + '\n'))
      }
      controller.close()
    },
  })

  return new Response(stream, {
    headers: {
      ...corsHeaders,
      'Content-Type': 'application/json',
      'Transfer-Encoding': 'chunked',
    },
  })
}
```

