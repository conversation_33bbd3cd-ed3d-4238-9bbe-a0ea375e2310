import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { supabase } from '../utils/supabaseClient';
import { User, Mail, Calendar, Award, MapPin } from 'lucide-react';
import Loader from '../components/Loader';

const PageWrapper = styled.div`
  background-color: #000;
  min-height: 100vh;
  color: white;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const ProfileContainer = styled.div`
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  background: #1a1a1a;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #333;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding-bottom: 1.5rem;
  }
`;

const ProfileImage = styled.div`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #D8DF20;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: 768px) {
    width: 120px;
    height: 120px;
  }
`;

const ProfileInfo = styled.div`
  flex: 1;

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const ProfileName = styled.h1`
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #D8DF20;

  @media (max-width: 768px) {
    font-size: 1.8rem;
  }
`;

const ProfileEmail = styled.p`
  font-size: 1.1rem;
  color: #999;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const InfoCard = styled.div`
  background: #000;
  padding: 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid #333;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: #D8DF20;
  }

  svg {
    color: #D8DF20;
    min-width: 24px;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const InfoContent = styled.div`
  h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 1.1rem;
    color: white;
  }

  @media (max-width: 768px) {
    h3 {
      font-size: 0.8rem;
    }

    p {
      font-size: 1rem;
    }
  }
`;

const ErrorMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: #ff4444;
  font-size: 1.2rem;
  background: rgba(255, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 68, 68, 0.2);

  @media (max-width: 768px) {
    padding: 1.5rem;
    font-size: 1rem;
  }
`;

function PerfilUsuario() {
  const { id } = useParams();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;
        setUserData(data);
      } catch (err) {
        setError('Error al cargar los datos del usuario');
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [id]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <>
        <Navbar />
        <PageWrapper>
          <ErrorMessage>{error}</ErrorMessage>
        </PageWrapper>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <PageWrapper>
        <ProfileContainer>
          <ProfileHeader>
            <ProfileImage>
              {userData?.avatar_url ? (
                <img 
                  src={userData.avatar_url} 
                  alt={`${userData.name} ${userData.lastname}`} 
                  loading="lazy"
                />
              ) : (
                <User size={60} color="#D8DF20" />
              )}
            </ProfileImage>
            <ProfileInfo>
              <ProfileName>{userData?.name || 'Usuario'}</ProfileName>
              <ProfileName>{userData?.lastname || 'Usuario'}</ProfileName>
              <ProfileEmail>{userData?.email}</ProfileEmail>
            </ProfileInfo>
          </ProfileHeader>

          <InfoGrid>
            <InfoCard>
              <Mail size={24} />
              <InfoContent>
                <h3>Email</h3>
                <p>{userData?.email}</p>
              </InfoContent>
            </InfoCard>

            <InfoCard>
              <Calendar size={24} />
              <InfoContent>
                <h3>Fecha de Registro</h3>
                <p>{new Date(userData?.created_at).toLocaleDateString()}</p>
              </InfoContent>
            </InfoCard>

            <InfoCard>
              <Award size={24} />
              <InfoContent>
                <h3>Puntos</h3>
                <p>{userData?.points || 0}</p>
              </InfoContent>
            </InfoCard>

            <InfoCard>
              <MapPin size={24} />
              <InfoContent>
                <h3>Ubicación</h3>
                <p>{userData?.location || 'No especificada'}</p>
              </InfoContent>
            </InfoCard>
          </InfoGrid>
        </ProfileContainer>
      </PageWrapper>
    </>
  );
}

export default PerfilUsuario; 