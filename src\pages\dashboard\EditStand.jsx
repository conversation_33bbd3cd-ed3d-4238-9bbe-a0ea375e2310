import React, { useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useUserStore } from '../../store/useUserStore';
import { useAuthStore } from '../../store/useAuthStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';
import { toast } from 'react-hot-toast';
import { 
  Mail, Phone, Instagram, Facebook, Linkedin, ExternalLink,
  Store, Link as LinkIcon, Image, User, MessageSquare, 
  Tag, Globe, Share2, CreditCard
} from 'lucide-react';
import ImageUpload from '../../components/ImageUpload';
import Loader from '../../components/Loader';

const Container = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
  background: #F5F5F5;
  min-height: calc(100vh - 60px);
  padding-bottom: 5vh;
`;

const PreviewSection = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  margin-bottom: 2rem;
  padding-bottom: 5vh;
`;

const FormSection = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

const Header = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  padding-bottom: 1.5rem;
`;

const SectionTitle = styled.h2`
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

const FormGroup = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  width: 100%;
  align-items: center;
`;

const FullWidthGroup = styled(FormGroup)`
  grid-column: 1 / -1;
`;

const Label = styled.label`
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;

  svg {
    width: 16px;
    height: 16px;
    color: #6c757d;
  }
`;

const Input = styled.input`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  transition: all 0.2s;
  width: 100%;
  max-width: 100%;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  min-height: 100px;
  resize: vertical;
  transition: all 0.2s;
  width: 100%;
  max-width: 100%;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const Button = styled(motion.button)`
  background: #D8DF20;
  color: #000;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  align-self: flex-start;

  &:hover {
    background: #c4cb1c;
    transform: translateY(-1px);
  }

  &:disabled {
    background: #e9ecef;
    color: #adb5bd;
    cursor: not-allowed;
  }
`;

const LoadingContainer = styled(motion.div)`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #D8DF20;
  font-size: 1.2rem;
`;

// Preview Components
const PreviewBanner = styled.div`
  width: 100%;
  height: 200px;
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  margin-bottom: 2rem;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: visible;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
  background-image: ${props => props.$hasImage ? `url(${props.$imageUrl})` : `url(/post-placeholder.webp)`};
  background-color: transparent;
`;

const AvatarContainer = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid white;
  position: absolute;
  bottom: 35%;
  left: 56%;
  transform: translateX(-50%);
  background-color: #f8f9fa;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 0.8rem;
  text-align: center;
  padding: 0.5rem;
  background-image: ${props => props.$hasImage ? `url(${props.$imageUrl})` : `url(/avatar.webp)`};
  background-size: cover;
  background-position: center;
`;

const PreviewLabel = styled.span`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  z-index: 3;
`;

const PreviewAvatar = styled.img`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid white;
  position: absolute;
  bottom: -50px;
  left: 30px;
  background-color: #f8f9fa;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
  object-fit: cover;
`;

const PreviewContent = styled.div`
  margin-top: 60px;
  padding: 0 30px;
  width: 100%;
  height:55vh;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
  z-index: 1;
`;

const PreviewName = styled.h2`
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const PreviewCategory = styled.span`
  display: inline-block;
  background: #D8DF20;
  color: #000;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.5rem;
`;

const PreviewDescription = styled.p`
  color: #6c757d;
  margin: 1.5rem 0;
  line-height: 1.6;
  font-size: 1rem;
`;

const PreviewContact = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
`;

const ContactItem = styled.a`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  text-decoration: none;
  transition: color 0.2s;
  font-size: 0.9rem;

  &:hover {
    color: #D8DF20;
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  margin-bottom: 1.5rem;

`;

const SocialIcon = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f8f9fa;
  color: #495057;
  transition: all 0.2s;
  border: 1px solid #dee2e6;

  &:hover {
    color: #D8DF20;
    transform: translateY(-2px);
    border-color: #D8DF20;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  grid-column: 1 / -1;
`;

const PreviewButton = styled(Button)`
  background: #0066cc;
  color: white;
  width: 100%;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover {
    background: #0052a3;
  }
`;

const UploadContainer = styled.div`
  // ... otros estilos ...
  ${props => props.$isAvatar && `
    // ... estilos específicos para avatar ...
  `}
`;

const EditStand = () => {
  const { 
    standId, 
    standData, 
    loading, 
    error,
    updateStandData,
    setLoading,
    setError
  } = useUserStore();
  const { user } = useAuthStore();
  const { language } = useLanguageStore();

  const [formData, setFormData] = React.useState({
    name: '',
    slug: '',
    banner_url: '',
    whatsapp: '',
    email: '',
    avatar: '',
    description: '',
    description_es: '',
    description_en: '',
    category: '',
    facebook: '',
    instagram: '',
    linkedin: '',
    'tarjeta-digital': '',
    ai_stands: ''
  });

  // Función para convertir valores null a strings vacíos
  const sanitizeData = (data) => {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = value === null ? '' : value;
    }
    return sanitized;
  };

  const fetchStandData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: standError } = await supabase
        .from('stands')
        .select('*')
        .eq('id', standId)
        .single();

      if (standError) {
        throw standError;
      }

      if (data) {
        setFormData(sanitizeData(data));
        updateStandData(data);
      } else {
        setError('No se encontró el stand');
      }
    } catch (error) {
      console.error('Error al cargar datos del stand:', error);
      setError('Error al cargar los datos del stand');
      toast.error(language === 'es' ? 'Error al cargar los datos del stand' : 'Error loading stand data');
    } finally {
      setLoading(false);
    }
  }, [standId, language, setLoading, setError, updateStandData]);

  useEffect(() => {
    if (standData) {
      setFormData(sanitizeData(standData));
    } else if (standId) {
      fetchStandData();
    } else {
      setError('No se encontró el ID del stand');
    }
  }, [standId, standData, fetchStandData, setError]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value || '' // Asegurar que el valor nunca sea null o undefined
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!standId) {
      toast.error(language === 'es' ? 'No se encontró el ID del stand' : 'Stand ID not found');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Filtrar solo los campos que tienen valor y asegurar que no sean null
      const dataToUpdate = Object.fromEntries(
        Object.entries(formData)
          .filter(([, value]) => value !== '')
          .map(([key, value]) => [key, value || ''])
      );

      // Agregar la fecha de actualización
      dataToUpdate.updated_at = new Date().toISOString();

      const { data, error: updateError } = await supabase
        .from('stands')
        .update(dataToUpdate)
        .eq('id', standId)
        .select();

      if (updateError) {
        throw updateError;
      }

      if (data && data.length > 0) {
        setFormData(sanitizeData(data[0]));
        updateStandData(data[0]);
        toast.success(language === 'es' ? 'Stand actualizado exitosamente' : 'Stand updated successfully');
      } else {
        throw new Error('No se recibieron datos actualizados');
      }
    } catch (error) {
      console.error('Error al actualizar el stand:', error);
      setError('Error al actualizar el stand: ' + error.message);
      toast.error((language === 'es' ? 'Error al actualizar el stand: ' : 'Error updating stand: ') + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (type, file) => {
    try {
      if (!user?.id) {
        throw new Error('No se encontró el ID del usuario');
      }

      setLoading(true);

      // Crear un nombre único para el archivo
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${type}-${Date.now()}.${fileExt}`;
      const filePath = `${fileName}`;

      // Subir la imagen al bucket correcto
      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Obtener la URL pública de la imagen
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      // Actualizar el stand con la nueva URL
      const { error: updateError } = await supabase
        .from('stands')
        .update({
          [type === 'avatar' ? 'avatar' : 'banner_url']: publicUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', standId);

      if (updateError) {
        throw updateError;
      }

      // Actualizar el estado local
      setFormData(prev => ({
        ...prev,
        [type === 'avatar' ? 'avatar' : 'banner_url']: publicUrl
      }));

      toast.success(
        language === 'es' 
          ? `${type === 'avatar' ? 'Avatar' : 'Banner'} actualizado exitosamente`
          : `${type === 'avatar' ? 'Avatar' : 'Banner'} updated successfully`
      );

    } catch (error) {
      console.error('Error al subir la imagen:', error);
      toast.error((language === 'es' ? 'Error al subir la imagen: ' : 'Error uploading image: ') + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <Container>
        <FormSection>
          <Header>
            <Title>Error</Title>
          </Header>
                      <p style={{ color: '#ff4444' }}>{error}</p>
          <Button
            onClick={fetchStandData}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {language === 'es' ? 'Reintentar' : 'Retry'}
          </Button>
        </FormSection>
      </Container>
    );
  }

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <PreviewSection>
        <Title>{language === 'es' ? 'Mi Perfil' : 'My Profile'}</Title>
        <PreviewLabel>{language === 'es' ? 'Ver mi Perfil' : 'View my Profile'}</PreviewLabel>
        <PreviewBanner $hasImage={!!formData.banner_url} $imageUrl={formData.banner_url}>
          <ImageUpload
            currentImage={formData.banner_url}
            onImageUpload={(file) => handleImageUpload('banner', file)}
            type="banner"
            userId={user?.id}
            $isAvatar={false}
            showPreview={false}
          />
        </PreviewBanner>
        
        <AvatarContainer $hasImage={!!formData.avatar} $imageUrl={formData.avatar}>
          <ImageUpload
            currentImage={formData.avatar}
            onImageUpload={(file) => handleImageUpload('avatar', file)}
            type="avatar"
            userId={user?.id}
            $isAvatar={true}
            showPreview={false}
          />
        </AvatarContainer>
        
        <PreviewContent>
          <PreviewName>{formData.name || (language === 'es' ? 'Nombre del Stand' : 'Stand Name')}</PreviewName>
          {formData.category && <PreviewCategory>{formData.category}</PreviewCategory>}
          <PreviewDescription>
            {language === 'es' 
              ? (formData.description_es || formData.description_en || formData.description || (language === 'es' ? 'Descripción del stand...' : 'Stand description...'))
              : (formData.description_en || formData.description_es || formData.description || (language === 'es' ? 'Descripción del stand...' : 'Stand description...'))
            }
          </PreviewDescription>
          
          <PreviewContact>
            {formData.email && (
              <ContactItem href={`mailto:${formData.email}`}>
                <Mail size={20} />
                {formData.email}
              </ContactItem>
            )}
            {formData.whatsapp && (
              <ContactItem href={`https://wa.me/${formData.whatsapp}`}>
                <Phone size={20} />
                {formData.whatsapp}
              </ContactItem>
            )}
          </PreviewContact>

          <SocialLinks>
            {formData.facebook && (
              <SocialIcon href={formData.facebook} target="_blank" rel="noopener noreferrer">
                <Facebook size={20} />
              </SocialIcon>
            )}
            {formData.instagram && (
              <SocialIcon href={formData.instagram} target="_blank" rel="noopener noreferrer">
                <Instagram size={20} />
              </SocialIcon>
            )}
            {formData.linkedin && (
              <SocialIcon href={formData.linkedin} target="_blank" rel="noopener noreferrer">
                <Linkedin size={20} />
              </SocialIcon>
            )}
          </SocialLinks>

          {formData['tarjeta-digital'] && (
            <Button
              as="a"
              href={formData['tarjeta-digital']}
              target="_blank"
              rel="noopener noreferrer"
              style={{ marginTop: '1rem', width: '100%', justifyContent: 'center' }}
            >
              <ExternalLink size={20} style={{ marginRight: '0.5rem' }} />
              {language === 'es' ? 'Ver Catálogo Digital' : 'View Digital Catalog'}
            </Button>
          )}
        </PreviewContent>
      </PreviewSection>

      <FormSection>
        <Header>
          <Title>{language === 'es' ? 'Editar Stand' : 'Edit Stand'}</Title>
        </Header>

        <Form onSubmit={handleSubmit}>
          <div>
            <SectionTitle>{language === 'es' ? 'Información Básica' : 'Basic Information'}</SectionTitle>
            <FormGroup>
              <Label>
                <Store />
                {language === 'es' ? 'Nombre del Stand' : 'Stand Name'}
              </Label>
              <Input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={language === 'es' ? 'Nombre del stand' : 'Stand name'}
              />

              <Label>
                <LinkIcon />
                Slug (URL amigable)
              </Label>
              <Input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                placeholder={language === 'es' ? 'URL amigable' : 'Friendly URL'}
              />

              <Label>
                <Tag />
                {language === 'es' ? 'Categoría' : 'Category'}
              </Label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                style={{
                  padding: '0.75rem',
                  background: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '6px',
                  color: '#212529',
                  fontSize: '0.95rem',
                  width: '100%',
                  maxWidth: '100%'
                }}
              >
                <option value="">{language === 'es' ? 'Selecciona una categoría' : 'Select a category'}</option>
                <option value="Restaurant">Restaurant</option>
                <option value="Beauty">Beauty</option>
                <option value="Retail">Retail</option>
              </select>
            </FormGroup>

            <div>
              <SectionTitle>{language === 'es' ? 'Descripción del Stand' : 'Stand Description'}</SectionTitle>
              
              {language === 'es' ? (
                <div style={{ color: '#6c757d', marginBottom: '1rem', fontSize: '0.95rem', lineHeight: '1.5' }}>
                  💡 <strong>Consejo:</strong> Completa la descripción en ambos idiomas (español e inglés) para que más visitantes puedan conocer tu stand.
                </div>
              ) : (
                <div style={{ color: '#6c757d', marginBottom: '1rem', fontSize: '0.95rem', lineHeight: '1.5' }}>
                  💡 <strong>Tip:</strong> Fill out the description in both languages (Spanish and English) so more visitors can learn about your stand.
                </div>
              )}

              <FormGroup>
                <Label>
                  <MessageSquare />
                  {language === 'es' ? 'Descripción (Español)' : 'Description (Spanish)'} *
                </Label>
                <TextArea
                  name="description_es"
                  value={formData.description_es}
                  onChange={handleChange}
                  placeholder={language === 'es' ? 'Describe tu stand en español...' : 'Describe your stand in Spanish...'}
                  required
                />

                <Label>
                  <MessageSquare />
                  {language === 'es' ? 'Descripción (Inglés)' : 'Description (English)'}
                </Label>
                <TextArea
                  name="description_en"
                  value={formData.description_en}
                  onChange={handleChange}
                  placeholder={language === 'es' ? 'Describe tu stand en inglés...' : 'Describe your stand in English...'}
                />
              </FormGroup>
            </div>
          </div>

          <div>
            <SectionTitle>{language === 'es' ? 'Información de Contacto' : 'Contact Information'}</SectionTitle>
            <FormGroup>
              <Label>
                <MessageSquare />
                WhatsApp
              </Label>
              <Input
                type="text"
                name="whatsapp"
                value={formData.whatsapp}
                onChange={handleChange}
                placeholder={language === 'es' ? 'Número de WhatsApp' : 'WhatsApp Number'}
              />

              <Label>
                <Mail />
                Email
              </Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder={language === 'es' ? 'Correo electrónico' : 'Email address'}
              />
            </FormGroup>
          </div>

          <div>
            <SectionTitle>{language === 'es' ? 'Redes Sociales' : 'Social Media'}</SectionTitle>
            <FormGroup>
              <Label>
                <Facebook />
                Facebook
              </Label>
              <Input
                type="url"
                name="facebook"
                value={formData.facebook}
                onChange={handleChange}
                placeholder={language === 'es' ? 'URL de Facebook' : 'Facebook URL'}
              />

              <Label>
                <Instagram />
                Instagram
              </Label>
              <Input
                type="url"
                name="instagram"
                value={formData.instagram}
                onChange={handleChange}
                placeholder={language === 'es' ? 'URL de Instagram' : 'Instagram URL'}
              />

              <Label>
                <Linkedin />
                LinkedIn
              </Label>
              <Input
                type="url"
                name="linkedin"
                value={formData.linkedin}
                onChange={handleChange}
                placeholder={language === 'es' ? 'URL de LinkedIn' : 'LinkedIn URL'}
              />
            </FormGroup>
          </div>

          <div>
            <SectionTitle>{language === 'es' ? 'Catálogo Digital' : 'Digital Catalog'}</SectionTitle>
            <FormGroup>
              <Label>
                <CreditCard />
                {language === 'es' ? 'URL del Catálogo Digital' : 'Digital Catalog URL'}
              </Label>
              <Input
                type="text"
                name="tarjeta-digital"
                value={formData['tarjeta-digital']}
                onChange={handleChange}
                placeholder={language === 'es' ? 'URL de tu catálogo digital' : 'Your digital catalog URL'}
              />
            </FormGroup>
          </div>

          <ButtonGroup>
            <Button
              type="submit"
              disabled={loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {loading 
                ? (language === 'es' ? 'Guardando...' : 'Saving...') 
                : (language === 'es' ? 'Guardar Cambios' : 'Save Changes')
              }
            </Button>
          </ButtonGroup>
        </Form>
      </FormSection>
    </Container>
  );
};

export default EditStand;