import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuthStore } from '../store/useAuthStore';
import { useUserStore } from '../store/useUserStore';
import { ChevronDown, LayoutDashboard, Menu, X, LogOut } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { useLanguageStore } from '../store/useLanguageStore';
import { FaUserCircle } from 'react-icons/fa';
import { FaFacebookF, FaInstagram, FaTiktok, FaChevronDown, FaGoogle } from 'react-icons/fa';
import { Mail } from 'lucide-react';

const UserDropdown = styled.div`
  position: relative;
  border: 2px solid #ffffff;
  padding: 0.5rem 2rem;
  background-color: #222222;
  border-radius: 100px;
  z-index: 10000;
`;

const DropdownButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  svg {
    transition: transform 0.3s;
    transform: ${props => props.$isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 120%;
  right: 0;
  background: #222;
  padding: 1rem 1rem;
  border-radius: 6px;
  border: 1px solid #ffffff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  z-index: 10001;
  display: flex;
  width: 200px;
  flex-direction: column;
  gap: 1.5vh;
  align-items: flex-start;
  min-width: 200px;

  button {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    padding: 0.5rem 0;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;

    &:hover {
      color: #f16925;
      padding-left: 0.5rem;
    }

    svg {
      flex-shrink: 0;
    }
  }
`;

const Nav = styled.nav`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: .5rem 2rem;
  background: #f16925;
  color: white;
  position: relative;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  

  
  @media (max-width: 900px) {
    padding: .5rem 2rem;
  }
  @media (max-width: 600px) {
    padding: .5rem 1rem;
  }
`;

const LeftGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 2.5rem;
`;

const MainLinks = styled.div`
  display: flex;
  align-items: center;
  gap: 2.2rem;
`;

const RightGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 2.5rem;
  justify-content: flex-end;
`;

const Logo = styled.img`
  width: 150px;
  

`;

const HamburgerButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;

  @media (max-width: 1024px) {
    display: block;
  }
`;

const StyledNavLink = styled(Link)`
  color: white;
  text-decoration: none;
  font-size: 20px;
  padding: 0.5rem;
  font-weight: 700;
  transition: color 0.2s;

  &:hover {
    color: #ffe3d1;
  }

  @media (max-width: 1024px) {
    width: 100%;
    text-align: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
  }
`;

const StyledButton = styled.button`
  background: transparent;
  color: #fff;
  border: 3px solid #fff;
  border-radius: 10px;
  padding: 0.7rem 1.2rem;
  font-size: 18px;
  font-weight: bold;
  margin-left: 0.5rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  &:hover {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: #fff;
  }


`;

const TextButton = styled.button`
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  margin-left: 0.5rem;
  cursor: pointer;
  padding: 0.7rem 2rem;
  transition: color 0.2s;
  &:hover {
    color: #ffe3d1;
    text-decoration: underline;
  }
`;

const LanguageButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 22px;
  cursor: pointer;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  padding: 0.2rem 0.5rem;


`;

const SocialIcons = styled.div`
  display: flex;
  align-items: center;
  gap: 1.2rem;
  margin-left: 1.5rem;
  
  @media (max-width: 1024px) {
    margin-left: 0;
    margin-top: 1rem;
    justify-content: center;
    width: 100%;
    display: flex;
  }
`;

const MoreButton = styled.button`
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 700;
  position: relative;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const MoreMenu = styled.div`
  position: absolute;
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  padding: 1.5rem 2rem;
  z-index: 3000;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  border: 1px solid rgba(0,0,0,0.05);
  
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #fff;
  }

  @media (max-width: 1200px) {
    left: auto;
    right: 0;
    transform: none;
    
    &::before {
      left: auto;
      right: 20px;
      transform: none;
    }
  }
  
  @media (max-width: 600px) {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    transform: none;
    min-width: unset;
    width: 100vw;
    border-radius: 0 0 12px 12px;
    padding: 1.2rem 1rem;
    align-items: center;
    
    &::before {
      display: none;
    }
  }
`;

const MoreMenuLink = styled(Link)`
  color: #f16925;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 0.6rem 0;
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(241, 105, 37, 0.08);
    color: #e05a1a;
    padding-left: 0.5rem;
    text-decoration: none;
  }
`;

const MoreContainer = styled.div`
  position: relative;
  display: inline-block;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const MoreMenuLangBtn = styled.button`
  background: #f16925;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 700;
  padding: 0.5rem 1.2rem;
  margin-top: 0.7rem;
  cursor: pointer;
`;

const MobileMenu = styled.div`
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #f16925;
  z-index: 1000;
  padding: 2rem;
  flex-direction: column;
  align-items: center;
  gap: 2rem;

  @media (max-width: 1024px) {
    display: ${props => props.$isOpen ? 'flex' : 'none'};
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
`;

const MobileNavLinks = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
`;

const DesktopOnly = styled.div`
  @media (max-width: 1024px) {
    display: none;
  }
`;

const MobileOnly = styled.div`
  display: none;
  @media (max-width: 1024px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 1rem;
  }
`;

const NavLinks = styled.div`
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 900px) {
    gap: 1.2rem;
  }

  @media (max-width: 1024px) {
    display: ${props => props.$isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #111;
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    align-items: stretch;
  }
`;

export const Navbar = () => {
  const user = useAuthStore((state) => state.user);
  const role = useUserStore((state) => state.role);
  const userData = useUserStore((state) => state.userData);
  const fetchUserData = useUserStore((state) => state.fetchUserData);
  const logout = useAuthStore((state) => state.logout);
  const [open, setOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();
  const { language, toggleLanguage } = useLanguageStore();

  useEffect(() => {
    if (user?.id) {
      fetchUserData(user.id);
    }
  }, [user, fetchUserData]);

  useEffect(() => {
    if (!showMore) return;
    const handleClick = (e) => {
      if (!e.target.closest('.more-container')) {
        setShowMore(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [showMore]);

  useEffect(() => {
    if (!open) return;
    const handleClick = (e) => {
      if (!e.target.closest('.user-dropdown')) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);



  const handleDashboardClick = () => {
    navigate('/dashboard');
    setOpen(false);
    setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    await logout();
    setOpen(false);
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <Nav>
      <a href="/">
        <Logo src="/logo-2.webp" alt="logo eme" loading="eager" fetchPriority="high" />
      </a>
      <HamburgerButton
        onClick={toggleMenu}
        aria-label={
          isMenuOpen
            ? language === 'es'
              ? 'Cerrar menú de navegación'
              : 'Close navigation menu'
            : language === 'es'
            ? 'Abrir menú de navegación'
            : 'Open navigation menu'
        }
        aria-expanded={isMenuOpen}
      >
        {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
      </HamburgerButton>
      <NavLinks $isOpen={isMenuOpen}>
        <StyledNavLink to="/stands-virtuales?category=Restaurant">
          {language === 'es' ? 'Comer' : 'Eat'}
        </StyledNavLink>
        <StyledNavLink to="/stands-virtuales?category=Retail">
          {language === 'es' ? 'Tiendas' : 'Shops'}
        </StyledNavLink>
        <StyledNavLink to="/stands-virtuales?category=Beauty">
          {language === 'es' ? 'Belleza' : 'Beauty'}
        </StyledNavLink>
        <MoreContainer className="more-container">
          <MoreButton className="more-btn" onClick={() => setShowMore((v) => !v)} title={language === 'es' ? 'Más opciones' : 'More options'}>
            {language === 'es' ? 'Más' : 'More'} <FaChevronDown style={{ transform: showMore ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s ease' }} />
          </MoreButton>
          {showMore && (
            <MoreMenu className="more-menu">
              <MoreMenuLink to="/aboutus">{language === 'es' ? 'Sobre Nosotros' : 'About Us'}</MoreMenuLink>
              <MoreMenuLink to="/map-plant1">{language === 'es' ? 'Mapas' : 'Maps'}</MoreMenuLink>
              <MoreMenuLink to="/stands-virtuales">{language === 'es' ? 'Explorar' : 'Explore'}</MoreMenuLink>
              <MoreMenuLink to="/events">{language === 'es' ? 'Eventos' : 'Events'}</MoreMenuLink>
              <MoreMenuLink to="/hours">{language === 'es' ? 'Horarios' : 'Hours'}</MoreMenuLink>
            </MoreMenu>
          )}
        </MoreContainer>
        <MobileOnly>
          <StyledNavLink to="/aboutus">{language === 'es' ? 'Sobre Nosotros' : 'About Us'}</StyledNavLink>
          <StyledNavLink to="/map-plant1">{language === 'es' ? 'Mapas' : 'Maps'}</StyledNavLink>
          <StyledNavLink to="/stands-virtuales">{language === 'es' ? 'Explorar' : 'Explore'}</StyledNavLink>
          <StyledNavLink to="/events">{language === 'es' ? 'Eventos' : 'Events'}</StyledNavLink>
          <StyledNavLink to="/hours">{language === 'es' ? 'Horarios' : 'Hours'}</StyledNavLink>
        </MobileOnly>
        <StyledButton onClick={() => window.location.href = '/#contact'}>
          <Mail style={{marginRight: 8, fontSize: 22}} />
          {language === 'es' ? 'Contacto' : 'Contact'}
        </StyledButton>
        
        {user ? (
          <UserDropdown className="user-dropdown" $isOpen={open}>
            <DropdownButton onClick={() => setOpen(!open)} $isOpen={open}>
              {userData?.name && userData?.lastname ? `${userData.name} ${userData.lastname}` : userData?.name || user.email} <ChevronDown />
            </DropdownButton>
            {open && (
              <DropdownMenu>
                {role === 'local' && (
                  <button onClick={handleDashboardClick}>
                    <LayoutDashboard size={16} />
                    Dashboard
                  </button>
                )}

                <button onClick={handleLogout}>
                  <LogOut size={16} />
                  {language === 'es' ? 'Cerrar Sesión' : 'Logout'}
                </button>
              </DropdownMenu>
            )}
          </UserDropdown>
        ) : (
          <StyledButton onClick={() => window.location.href = '/login'}>
            <FaUserCircle style={{marginRight: 8, fontSize: 22}} />
            {language === 'es' ? 'Portal de Inquilinos' : 'Tenant Portal'}
          </StyledButton>
        )}
        
        <LanguageButton onClick={toggleLanguage} title={language === 'es' ? 'Cambiar a inglés' : 'Switch to Spanish'}>
          {language === 'es' ? 'EN' : 'ES'}
        </LanguageButton>
        <SocialIcons>
          <a href="https://g.co/kgs/9bdPKQE" target="_blank" rel="noopener noreferrer" aria-label="Google Business"><FaGoogle size={22} color="#fff" /></a>
          <a href="https://www.facebook.com/mercadodistrict/?locale=es_LA" target="_blank" rel="noopener noreferrer" aria-label="Facebook"><FaFacebookF size={22} color="#fff" /></a>
          <a href="https://www.instagram.com/mercadodistrict/?hl=es" target="_blank" rel="noopener noreferrer" aria-label="Instagram"><FaInstagram size={22} color="#fff" /></a>
          <a href="https://www.tiktok.com/@themercadodistrict" target="_blank" rel="noopener noreferrer" aria-label="TikTok"><FaTiktok size={22} color="#fff" /></a>
        </SocialIcons>
      </NavLinks>
    </Nav>
  );
};
