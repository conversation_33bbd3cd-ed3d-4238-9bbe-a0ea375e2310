import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUserStore } from '../../store/useUserStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';
import { 
  Users, MessageSquare, Package, Gift, 
  TrendingUp, Calendar, Clock, Activity
} from 'lucide-react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title as ChartTitle,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ChartTitle,
  Tooltip,
  Legend
);

const Container = styled(motion.div)`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: 1024px) {
    padding: 1rem;
  }
`;

const DashboardTitle = styled.h1`
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;

  @media (max-width: 768px) {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const MetricCard = styled(motion.div)`
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const MetricTitle = styled.h3`
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const MetricValue = styled.span`
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
`;

const ChartContainer = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
    margin-top: 1.5rem;
  }
`;

const ChartSectionTitle = styled.h2`
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;

  @media (max-width: 768px) {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
`;

const Home = () => {
  const { standId } = useUserStore();
  const { language } = useLanguageStore();
  const [metrics, setMetrics] = useState({
    totalVisits: 0,
    aiInteractions: 0,
    pointsGiven: 0
  });
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (standId) {
      fetchAnalytics();
    }
  }, [standId]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // Obtener métricas generales
      const { data: analyticsData, error: analyticsError } = await supabase
        .from('stand_analytics')
        .select('event_type, created_at')
        .eq('stand_id', standId);

      if (analyticsError) throw analyticsError;

      // Obtener puntos dados
      const { data: pointsData, error: pointsError } = await supabase
        .from('stand_points')
        .select('created_at')
        .eq('stand_id', standId);

      if (pointsError) throw pointsError;

      // Calcular métricas
      const metrics = {
        totalVisits: analyticsData.filter(a => a.event_type === 'visit').length,
        aiInteractions: analyticsData.filter(a => a.event_type === 'ai_interaction').length,
        pointsGiven: pointsData.length
      };

      setMetrics(metrics);

      // Preparar datos para la gráfica
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse();

      const visitsByDay = last7Days.map(date => {
        return analyticsData.filter(a => 
          a.event_type === 'visit' && 
          a.created_at.split('T')[0] === date
        ).length;
      });

      setChartData({
        labels: last7Days.map(date => new Date(date).toLocaleDateString(language === 'es' ? 'es-ES' : 'en-US', { weekday: 'short' })),
        datasets: [{
          label: language === 'es' ? 'Visitas' : 'Visits',
          data: visitsByDay,
          borderColor: '#D8DF20',
          tension: 0.4,
          fill: false
        }]
      });
    } catch (error) {
      console.error('Error al cargar analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>{language === 'es' ? 'Cargando...' : 'Loading...'}</div>;
  }

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <DashboardTitle>Dashboard</DashboardTitle>

      <MetricsGrid>
        <MetricCard
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <MetricTitle>
            <Users size={20} />
            {language === 'es' ? 'Visitas Totales' : 'Total Visits'}
          </MetricTitle>
          <MetricValue>{metrics.totalVisits}</MetricValue>
        </MetricCard>

        <MetricCard
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <MetricTitle>
            <MessageSquare size={20} />
            {language === 'es' ? 'Interacciones IA' : 'AI Interactions'}
          </MetricTitle>
          <MetricValue>{metrics.aiInteractions}</MetricValue>
        </MetricCard>

        <MetricCard
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <MetricTitle>
            <Gift size={20} />
            {language === 'es' ? 'Puntos Otorgados' : 'Points Given'}
          </MetricTitle>
          <MetricValue>{metrics.pointsGiven}</MetricValue>
        </MetricCard>
      </MetricsGrid>

      <ChartContainer>
        <ChartSectionTitle>{language === 'es' ? 'Visitas en los últimos 7 días' : 'Visits in the last 7 days'}</ChartSectionTitle>
        {chartData && (
          <Line
            data={chartData}
            options={{
              responsive: true,
              plugins: {
                legend: {
                  position: 'top',
                },
                title: {
                  display: false
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    stepSize: 1
                  }
                }
              }
            }}
          />
        )}
      </ChartContainer>
    </Container>
  );
};

export default Home; 