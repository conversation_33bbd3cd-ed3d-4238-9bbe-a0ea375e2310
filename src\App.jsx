import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { useEffect, Suspense, lazy } from 'react'
import { useAuthStore } from './store/useAuthStore'
import { useUserStore } from './store/useUserStore'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import { ReactPlugin } from '@stagewise-plugins/react'
import { Toaster } from 'react-hot-toast';
import LoadingSpinner from './components/LoadingSpinner'
import PasswordResetRedirect from './components/PasswordResetRedirect'
import { preloadCriticalImages } from './utils/imageOptimization'

// Lazy load all pages for better performance
const Login = lazy(() => import('./pages/Login'))
const Home = lazy(() => import('./pages/Home'))
const Register = lazy(() => import('./pages/Register'))
const Eme2025Gate = lazy(() => import('./pages/Eme2025Gate'))
const VerifyEmail = lazy(() => import('./pages/VerifyEmail'))
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'))
const ResetPassword = lazy(() => import('./pages/ResetPassword'))
const TestPasswordReset = lazy(() => import('./pages/TestPasswordReset'))
const InteractiveMap = lazy(() => import('./pages/InteractiveMap').then(module => ({ default: module.InteractiveMap })))
const Itinerary = lazy(() => import('./pages/Itinerary').then(module => ({ default: module.Itinerary })))
const StandsVirtuales = lazy(() => import('./pages/StandsVirtuales'))
const StandVirtualDetalle = lazy(() => import('./pages/StandVirtualDetalle'))
const ConsultaPuntos = lazy(() => import('./pages/ConsultaPuntos'))
const ConsultarPuntosDetalle = lazy(() => import('./pages/ConsultarPuntosDetalle'))
const RevivePlus = lazy(() => import('./pages/RevivePlus'))
const ReviveDetalle = lazy(() => import('./pages/ReviveDetalle'))
const ReproducirVideo = lazy(() => import('./pages/ReproducirVideo'))
const PerfilUsuario = lazy(() => import('./pages/PerfilUsuario'))
const Recompensas = lazy(() => import('./pages/Recompensas').then(module => ({ default: module.Recompensas })))
const Welcome = lazy(() => import('./pages/Welcome'))
const MyPanel = lazy(() => import('./pages/MyPanel'))
const Events = lazy(() => import('./pages/Events'))
const EventDetails = lazy(() => import('./pages/EventDetails'))
const AboutUs = lazy(() => import('./pages/AboutUs'))
const Hours = lazy(() => import('./pages/Hours'))
const MapPlant1 = lazy(() => import('./components/MapPlant1'))
const TenantDetail = lazy(() => import('./pages/TenantDetail'))

import './App.css'

function App() {
  const checkUser = useAuthStore((state) => state.checkUser)
  const setUser = useAuthStore((state) => state.setUser)
  const initializeAuthListener = useAuthStore((state) => state.initializeAuthListener)
  const fetchUserData = useUserStore((state) => state.fetchUserData)

  useEffect(() => {
    // Initialize authentication state and listener on app startup
    const initializeAuth = async () => {
      try {
        // Set up the auth state change listener
        initializeAuthListener()
        
        // Check current user session
        const { user } = await checkUser()
        if (user) {
          setUser(user)
          // Fetch additional user data if authenticated
          await fetchUserData(user.id)
        }
      } catch (error) {
        console.error('Error initializing authentication:', error)
      }
    }

    // Preload critical images for better performance
    preloadCriticalImages()

    initializeAuth()
  }, [checkUser, setUser, initializeAuthListener, fetchUserData])

  return (
    <>
      <StagewiseToolbar 
        config={{
          plugins: [ReactPlugin]
        }}
      />
      <BrowserRouter>
        <Toaster position="top-center" reverseOrder={false} />
        <Suspense fallback={<LoadingSpinner message="Cargando página..." />}>
          <Routes>
            <Route path="/" element={
              <>
                <PasswordResetRedirect />
                <Home />
              </>
            } />
            <Route path="/login" element={<Login/>} />
            <Route path="/signup" element={<Register />} />
            <Route path="/verifica-tu-correo" element={<VerifyEmail />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/test-password-reset" element={<TestPasswordReset />} />
            <Route path='/interactive-map' element={<InteractiveMap />} />
            <Route path='/map-plant1' element={<MapPlant1 />} />
            <Route path='/itinerary' element={<Itinerary  />} />
            <Route path='/stands-virtuales' element={<StandsVirtuales  />} />
            <Route path='/stands-virtuales/:slug' element={<StandVirtualDetalle  />} />
            <Route path='/consulta-puntos' element={<ConsultaPuntos  />} />
            <Route path='/consultar-puntos/:email' element={<ConsultarPuntosDetalle  />} />
            <Route path='/revive-plus' element={<RevivePlus  />} />
            <Route path='/revive-detalle/:id' element={<ReviveDetalle  />} />
            <Route path='/reproducir/:id' element={<ReproducirVideo  />} />
            <Route path='/perfil/:id' element={<PerfilUsuario  />} />
            <Route path='/recompensas' element={<Recompensas />} />
            <Route path='/welcome' element={<Welcome />} />
            <Route path='/dashboard/*' element={<MyPanel />} />
            <Route path='/events' element={<Events />} />
            <Route path='/eventinfo/:id' element={<EventDetails />} />
            <Route path='/aboutus' element={<AboutUs />} />
            <Route path='/hours' element={<Hours />} />
            <Route path='/tenant/:slug' element={<TenantDetail />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </>
  )
}

export default App
