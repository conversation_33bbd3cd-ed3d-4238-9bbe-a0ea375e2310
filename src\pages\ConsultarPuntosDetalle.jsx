import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { UserCircle, Award, ArrowLeft, Calendar, AlertTriangle, RefreshCw, Mail } from 'lucide-react';
import { supabase } from '../utils/supabaseClient';
import toast from 'react-hot-toast';

const PageWrapper = styled.div`
  background-color: #000;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #fff;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  color: #D8DF20;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
`;

const ResultCard = styled.div`
  background: #1a1a1a;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  border: 1px solid #333;

  @media (max-width: 768px) {
    padding: 1.5rem;
    border-radius: 10px;
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`;

const UserAvatar = styled.div`
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: #D8DF20;

  @media (max-width: 768px) {
    width: 80px;
    height: 80px;
  }
`;

const UserName = styled.h2`
  font-size: 1.8rem;
  margin: 0 0 0.5rem 0;
  color: #fff;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const UserEmail = styled.p`
  color: #999;
  margin: 0 0 1rem 0;
`;

const UserMeta = styled.div`
  color: #666;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
`;

const PointsContainer = styled.div`
  background-color: #000;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #333;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const PointsTitle = styled.h3`
  font-size: 1.2rem;
  color: #D8DF20;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PointsValue = styled.div`
  font-size: 3rem;
  font-weight: bold;
  color: #D8DF20;
  text-shadow: 0 0 10px rgba(216, 223, 32, 0.3);

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const ErrorContainer = styled.div`
  width: 100%;
  max-width: 600px;
  margin: 1rem auto;
  text-align: center;
`;

const ErrorMessage = styled.div`
  color: #ff4444;
  padding: 1.5rem;
  border-radius: 12px;
  background-color: rgba(255, 68, 68, 0.1);
  margin-bottom: 1.5rem;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  border: 1px solid rgba(255, 68, 68, 0.2);

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const ErrorTitle = styled.h3`
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ff4444;
`;

const ErrorDetail = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: #ff8888;
`;

const BackButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background-color: #1a1a1a;
  color: #fff;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid #333;
  margin-bottom: 2rem;
  width: fit-content;
  align-self: flex-start;

  &:hover {
    background-color: #D8DF20;
    color: #000;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 768px) {
    gap: 0.5rem;
  }
`;

const RetryButton = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background-color: #D8DF20;
  color: #000;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #e8ef30;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
`;

const SearchNewButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid #333;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    background-color: #D8DF20;
    color: #000;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  padding: 2rem;
  background-color: #1a1a1a;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  border: 1px solid #333;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const SpinnerIcon = styled(RefreshCw)`
  animation: spin 1.5s linear infinite;
  color: #D8DF20;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.p`
  color: #999;
  font-size: 1.2rem;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const formatDate = (dateString) => {
  if (!dateString) return 'Fecha no disponible';
  
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

const ConsultarPuntosDetalle = () => {
  const { email } = useParams();
  const [usuario, setUsuario] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const toastShown = useRef(false);

  const fetchUserData = async () => {
    setLoading(true);
    setError(null);
    toastShown.current = false;
    
    try {
      // Decodificamos el email de la URL
      const decodedEmail = decodeURIComponent(email);
      
      console.log('Consultando usuario con email:', decodedEmail);
      
      // Validar que el email tenga un formato válido
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(decodedEmail)) {
        setError('El formato del correo electrónico no es válido');
        setLoading(false);
        return;
      }
      
      // Realizamos la consulta a Supabase
      const { data, error: supabaseError } = await supabase
        .from('users')
        .select('id, name, lastname, email, points, role, created_at')
        .eq('email', decodedEmail.toLowerCase());
      
      console.log('Resultado de consulta:', data, supabaseError);
      
      if (supabaseError) {
        throw supabaseError;
      }
      
      if (!data || data.length === 0) {
        setError(`No se encontró ningún usuario con el correo ${decodedEmail}`);
        setUsuario(null);
      } else {
        setUsuario(data[0]);
        setError(null);
      }
    } catch (err) {
      console.error('Error al consultar usuario:', err);
      
      // Mensajes de error más amigables según el tipo de error
      if (err.code === '42P01') {
        setError('La tabla de usuarios no existe en la base de datos. Contacte al administrador.');
      } else if (err.code === 'PGRST116') {
        setError(`No se encontró ningún usuario con el correo proporcionado.`);
      } else {
        setError(`Error al consultar usuario: ${err.message || 'Error desconocido'}`);
      }
      
      setUsuario(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, [email]);

  useEffect(() => {
    // Mostrar el toast solo cuando el usuario se carga y no se ha mostrado antes
    if (usuario && !toastShown.current) {
      toast.success('Información de usuario cargada correctamente');
      toastShown.current = true;
    }
  }, [usuario]);

  const handleNewSearch = () => {
    navigate('/consulta-puntos');
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <BackButton to="/consulta-puntos">
          <ArrowLeft size={18} />
          Volver
        </BackButton>
        
        <Title>Detalles de Puntos</Title>
        
        {loading && (
          <LoadingContainer>
            <SpinnerIcon size={40} />
            <LoadingText>Cargando información del usuario...</LoadingText>
          </LoadingContainer>
        )}
        
        {error && (
          <ErrorContainer>
            <ErrorMessage>
              <ErrorTitle>
                <AlertTriangle size={20} />
                Error de consulta
              </ErrorTitle>
              <ErrorDetail>{error}</ErrorDetail>
              <ActionButtonsContainer>
                <RetryButton onClick={fetchUserData}>
                  <RefreshCw size={16} />
                  Intentar nuevamente
                </RetryButton>
                <SearchNewButton to="/consulta-puntos">
                  <Mail size={16} />
                  Buscar otro correo
                </SearchNewButton>
              </ActionButtonsContainer>
            </ErrorMessage>
          </ErrorContainer>
        )}
        
        {usuario && (
          <ResultCard>
            <UserInfo>
              <UserAvatar>
                <UserCircle size={100} />
              </UserAvatar>
              <UserName>
                {usuario.name || ''} {usuario.lastname || ''}
              </UserName>
              <UserEmail>{usuario.email}</UserEmail>
              <UserMeta>
                <Calendar size={14} />
                Usuario desde: {formatDate(usuario.created_at)}
              </UserMeta>
            </UserInfo>
            
            <PointsContainer>
              <PointsTitle>
                <Award size={20} />
                Puntos Acumulados
              </PointsTitle>
              <PointsValue>{usuario.points || 0}</PointsValue>
            </PointsContainer>
          </ResultCard>
        )}
      </PageWrapper>
    </>
  );
};

export default ConsultarPuntosDetalle; 