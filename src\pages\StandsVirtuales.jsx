import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Search, SlidersHorizontal, MapPin, Clock, ArrowUp, ArrowDown, X, Building2 } from 'lucide-react';
import { Link, useLocation, useSearchParams } from 'react-router-dom';
import { useStandsStore } from '../store/useStandsStore';
import { useLanguageStore } from '../store/useLanguageStore';
import Loader from '../components/Loader';
import { FaStore, FaUtensils, FaSpa, FaThLarge } from 'react-icons/fa';
import { Footer } from '../components/Footer';
import { useTenantListingsStore } from '../store/useTenantListingsStore';

const PageWrapper = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
`;

const HeaderSection = styled.div`
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 0;
`;

const HeaderContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
  }
`;

const SearchContainer = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 600px;
  position: relative;

  @media (max-width: 768px) {
    width: 100%;
    max-width: none;
  }
`;

const SearchBox = styled.div`
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border-color 0.3s ease;
  min-height: 44px;

  &:focus-within {
    border-color: #F16925;
  }

  @media (max-width: 768px) {
    padding: 0.7rem 0.9rem;
    min-height: 48px;
    border-radius: 6px;
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    min-height: 44px;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  border: none;
  font-size: 1rem;
  outline: none;
  margin-left: 0.5rem;
  background: transparent;
  color: #495057;

  &::placeholder {
    color: #6c757d;
  }

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-left: 0.4rem;
    
    &::placeholder {
      font-size: 0.9rem;
    }
  }

  @media (max-width: 480px) {
    font-size: 0.85rem;
    
    &::placeholder {
      font-size: 0.85rem;
    }
  }
`;

const SearchButton = styled.button`
  background: #F16925;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  margin-left: 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;

  &:hover {
    background: #e05a1a;
  }

  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    margin-left: 0.5rem;
    display: none; // Hide search button on mobile, search on type instead
  }
`;

const FilterToggle = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: ${props => props.$active ? '#F16925' : 'white'};
  border: 2px solid ${props => props.$active ? '#F16925' : '#dee2e6'};
  color: ${props => props.$active ? 'white' : '#495057'};
  border-radius: 8px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 44px;

  &:hover {
    border-color: #F16925;
    color: ${props => props.$active ? 'white' : '#F16925'};
    background: ${props => props.$active ? '#e05a1a' : '#f8f9fa'};
  }

  &:active {
    transform: scale(0.98);
  }

  @media (max-width: 768px) {
    flex: 1;
    min-width: 100px;
    font-size: 0.85rem;
    padding: 0.7rem 0.8rem;
    min-height: 48px;
    
    &:hover {
      background: ${props => props.$active ? '#F16925' : 'white'};
    }
    
    &:active {
      background: ${props => props.$active ? '#e05a1a' : '#f8f9fa'};
    }
  }

  @media (max-width: 480px) {
    min-width: auto;
    font-size: 0.8rem;
    padding: 0.6rem 0.7rem;
    gap: 0.4rem;
  }
`;

const SortContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-wrap: wrap;
    width: 100%;
    gap: 0.5rem;
  }
`;

const SortSelect = styled.select`
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  outline: none;
  min-width: 150px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;

  &:focus {
    border-color: #F16925;
  }

  @media (max-width: 768px) {
    flex: 1;
    min-width: 100px;
    font-size: 0.85rem;
    padding: 0.7rem 0.8rem;
    padding-right: 30px;
    background-size: 14px;
    background-position: right 8px center;
  }

  @media (max-width: 480px) {
    min-width: auto;
    font-size: 0.8rem;
    padding: 0.6rem 0.7rem;
    padding-right: 28px;
  }
`;

const MainContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: ${props => props.$sidebarOpen ? '280px 1fr' : '1fr'};
  gap: 2rem;
  position: relative;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
`;

const SidebarOverlay = styled.div`
  display: none;
  
  @media (max-width: 1024px) {
    display: ${props => props.$isOpen ? 'block' : 'none'};
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
`;

const Sidebar = styled.div`
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  display: ${props => props.$isOpen ? 'block' : 'none'};

  @media (min-width: 1025px) {
    display: ${props => props.$isOpen ? 'block' : 'none'};
  }

  @media (max-width: 1024px) {
    position: fixed;
    top: 0;
    left: 0;
    width: 85%;
    max-width: 350px;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
    border-radius: 0;
    transform: translateX(${props => props.$isOpen ? '0' : '-100%'});
    transition: transform 0.3s ease-in-out;
    display: block;
    padding: 1rem;
    padding-top: 3rem;
  }

  @media (max-width: 480px) {
    width: 90%;
    max-width: 280px;
    padding: 0.8rem;
    padding-top: 2.5rem;
  }
`;

const SidebarCloseButton = styled.button`
  display: none;
  
  @media (max-width: 1024px) {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #F16925;
      border-color: #F16925;
      color: white;
    }
  }
`;

const SidebarTitle = styled.h3`
  font-size: 1.2rem;
  font-weight: 700;
  color: #212529;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #F16925;

  @media (max-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
`;

const FilterSection = styled.div`
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const FilterLabel = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 1rem 0;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
  }

  @media (max-width: 480px) {
    font-size: 0.85rem;
    margin-bottom: 0.7rem;
  }
`;

const FilterOptions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FilterOption = styled.button`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: ${props => props.$active ? '#F16925' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#495057'};
  border: 1px solid ${props => props.$active ? '#F16925' : '#dee2e6'};
  border-radius: 8px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.9rem;
  width: 100%;
  min-height: 44px;

  &:hover {
    background: ${props => props.$active ? '#e05a1a' : '#f8f9fa'};
    border-color: #F16925;
  }

  &:active {
    transform: scale(0.98);
  }

  svg {
    font-size: 16px;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    padding: 0.8rem 0.9rem;
    font-size: 0.85rem;
    min-height: 48px;
    
    &:hover {
      background: ${props => props.$active ? '#F16925' : 'transparent'};
    }
    
    &:active {
      background: ${props => props.$active ? '#e05a1a' : '#f8f9fa'};
    }
  }

  @media (max-width: 480px) {
    gap: 0.6rem;
    padding: 0.7rem 0.8rem;
    font-size: 0.8rem;
  }
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
`;

const ResultsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;

  @media (max-width: 768px) {
    justify-content: center;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }
`;

const ResultsCount = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #212529;
  margin: 0;
`;

const ViewToggleContainer = styled.div`
  display: flex;
  justify-content: center;
  width: 100%;

  @media (min-width: 769px) {
    display: none;
  }

  @media (max-width: 768px) {
    margin-top: 1rem;
    padding: 0 1rem;
  }
`;

const ViewToggle = styled.div`
  display: flex;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;

  @media (max-width: 768px) {
    width: 100%;
    max-width: 300px;
  }

  @media (max-width: 480px) {
    max-width: 250px;
  }
`;

const DesktopViewToggle = styled.div`
  display: flex;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;

  @media (max-width: 768px) {
    display: none;
  }
`;

const ViewButton = styled.button`
  background: ${props => props.$active ? '#F16925' : 'white'};
  color: ${props => props.$active ? 'white' : '#495057'};
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;

  &:hover {
    background: ${props => props.$active ? '#e05a1a' : '#f8f9fa'};
  }

  @media (max-width: 768px) {
    font-size: 0.85rem;
    padding: 0.6rem 0.8rem;
  }
`;

const StoresGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.2rem;
    padding: 0 0.8rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  @media (max-width: 375px) {
    padding: 0 0.3rem;
    gap: 0.8rem;
  }
`;

const StoresList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const StoreCard = styled.div`
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #F16925;
  }

  &:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
  }

  @media (max-width: 768px) {
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(241, 105, 37, 0.08);
    margin-bottom: 0.5rem;
    background: #ffffff;
    position: relative;
    overflow: visible;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(241, 105, 37, 0.02) 0%, rgba(241, 105, 37, 0.01) 100%);
      border-radius: 20px;
      pointer-events: none;
    }
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: scale(0.96);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  @media (max-width: 480px) {
    border-radius: 18px;
    box-shadow: 0 6px 28px rgba(0, 0, 0, 0.05);
    
    &::before {
      border-radius: 18px;
    }
  }
`;

const StoreImageContainer = styled.div`
  position: relative;
  height: 160px;
  overflow: hidden;

  @media (max-width: 768px) {
    height: 150px;
    border-radius: 20px 20px 0 0;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.1) 0%, transparent 100%);
      pointer-events: none;
    }
  }

  @media (max-width: 480px) {
    height: 130px;
    border-radius: 18px 18px 0 0;
    
    &::after {
      height: 35px;
    }
  }
`;

const StoreBanner = styled.div`
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    background-color: #f8f9fa;
  }
`;

const StoreBannerImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const StoreLogoWrapper = styled.div`
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid white;

  @media (max-width: 768px) {
    width: 64px;
    height: 64px;
    bottom: -32px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: 4px solid white;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    z-index: 10;
    
    &::before {
      content: '';
      position: absolute;
      inset: -2px;
      background: linear-gradient(135deg, rgba(241, 105, 37, 0.1), rgba(241, 105, 37, 0.05));
      border-radius: 50%;
      z-index: -1;
    }
  }

  @media (max-width: 480px) {
    width: 56px;
    height: 56px;
    bottom: -28px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  }
`;

const StoreLogo = styled.img`
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }

  @media (max-width: 768px) {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    position: relative;
    z-index: 5;
  }

  @media (max-width: 480px) {
    width: 42px;
    height: 42px;
  }
`;

const StoreContent = styled.div`
  padding: 2rem 1.5rem 1.5rem;
  text-align: center;

  @media (max-width: 768px) {
    padding: 2.2rem 1.5rem 1.5rem;
    position: relative;
    z-index: 1;
  }

  @media (max-width: 480px) {
    padding: 2rem 1.2rem 1.2rem;
  }
`;

const StoreName = styled.h3`
  font-size: 1.1rem;
  font-weight: 700;
  color: #212529;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -0.01em;
    line-height: 1.2;
  }

  @media (max-width: 480px) {
    font-size: 1.05rem;
    margin-bottom: 0.25rem;
  }
`;

const StoreCategory = styled.div`
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0.8rem;

  @media (max-width: 768px) {
    font-size: 0.85rem;
    margin-bottom: 1rem;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(241, 105, 37, 0.08);
    color: #F16925;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 600;
  }

  @media (max-width: 480px) {
    font-size: 0.7rem;
    margin-bottom: 0.8rem;
    padding: 0.25rem 0.7rem;
    border-radius: 10px;
  }
`;

const StoreUnit = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  margin: 0.5rem auto;
  padding: 0.4rem 0.8rem;
  background: linear-gradient(135deg, rgba(241, 105, 37, 0.08) 0%, rgba(241, 105, 37, 0.04) 100%);
  border: 1px solid rgba(241, 105, 37, 0.15);
  border-radius: 8px;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
  width: fit-content;
  max-width: 90%;

  &:hover {
    background: linear-gradient(135deg, rgba(241, 105, 37, 0.12) 0%, rgba(241, 105, 37, 0.06) 100%);
    border-color: rgba(241, 105, 37, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(241, 105, 37, 0.15);
  }

  @media (max-width: 768px) {
    margin: 0.4rem auto;
    padding: 0.35rem 0.7rem;
    font-size: 0.8rem;
    border-radius: 6px;
    gap: 0.3rem;
  }

  @media (max-width: 480px) {
    margin: 0.3rem auto;
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
    border-radius: 5px;
    gap: 0.25rem;
  }
`;

const UnitIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: #F16925;
  opacity: 0.8;

  @media (max-width: 768px) {
    opacity: 0.9;
  }
`;

const UnitLabel = styled.span`
  color: #495057;
  font-weight: 500;
  margin-right: 0.2rem;
`;

const UnitNumbers = styled.span`
  color: #2c3e50;
  font-weight: 700;
  letter-spacing: 0.5px;
`;

const StoreHours = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #28a745;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    margin-bottom: 0.6rem;
    gap: 0.4rem;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 0.3rem 0.8rem;
    border-radius: 10px;
    display: inline-flex;
    font-weight: 600;
  }

  @media (max-width: 480px) {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0.7rem;
    border-radius: 8px;
  }
`;

const StoreLocation = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    margin-bottom: 1.2rem;
    gap: 0.4rem;
    color: #666;
    background: rgba(108, 117, 125, 0.08);
    padding: 0.3rem 0.8rem;
    border-radius: 10px;
    display: inline-flex;
    font-weight: 500;
  }

  @media (max-width: 480px) {
    font-size: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.25rem 0.7rem;
    border-radius: 8px;
  }
`;

const ViewStoreButton = styled(Link)`
  display: inline-block;
  background: #F16925;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #e05a1a;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    background: #d44e14;
  }

  @media (max-width: 768px) {
    padding: 1rem 2rem;
    font-size: 0.9rem;
    border-radius: 14px;
    font-weight: 700;
    width: 100%;
    max-width: 220px;
    margin: 0 auto;
    box-shadow: 0 4px 16px rgba(241, 105, 37, 0.25);
    background: linear-gradient(135deg, #F16925 0%, #e05a1a 100%);
    border: none;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }
    
    &:hover {
      transform: none;
    }
    
    &:active {
      background: linear-gradient(135deg, #d44e14 0%, #c44a14 100%);
      transform: scale(0.96);
      box-shadow: 0 2px 8px rgba(241, 105, 37, 0.4);
      transition: all 0.15s ease-out;
    }
  }

  @media (max-width: 480px) {
    padding: 0.9rem 1.8rem;
    font-size: 0.85rem;
    max-width: 200px;
    border-radius: 12px;
  }
`;

const DesktopListButton = styled(Link)`
  display: inline-block;
  background: #F16925;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #e05a1a;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    background: #d44e14;
  }

  @media (max-width: 768px) {
    display: none;
  }
`;

const StoreListItem = styled.div`
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  display: grid;
  grid-template-columns: 80px 1fr auto;
  gap: 1.5rem;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #F16925;
  }

  &:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
  }

  @media (max-width: 768px) {
    grid-template-columns: 70px 1fr;
    gap: 0;
    padding: 0;
    border-radius: 20px;
    border: 1px solid rgba(241, 105, 37, 0.08);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    background: #ffffff;
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(241, 105, 37, 0.02) 0%, rgba(241, 105, 37, 0.01) 100%);
      border-radius: 20px;
      pointer-events: none;
    }
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: scale(0.96);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  @media (max-width: 480px) {
    border-radius: 18px;
    box-shadow: 0 6px 28px rgba(0, 0, 0, 0.05);
    grid-template-columns: 65px 1fr;
    
    &::before {
      border-radius: 18px;
    }
  }
`;

const ListStoreLogo = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #F16925;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }

  @media (max-width: 768px) {
    width: 56px;
    height: 56px;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin: 1rem 0 1rem 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
    z-index: 2;
    
    &::before {
      content: '';
      position: absolute;
      inset: -2px;
      background: linear-gradient(135deg, rgba(241, 105, 37, 0.1), rgba(241, 105, 37, 0.05));
      border-radius: 50%;
      z-index: -1;
    }
  }

  @media (max-width: 480px) {
    width: 50px;
    height: 50px;
    margin: 0.8rem 0 0.8rem 0.8rem;
  }
`;

const ListStoreInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  @media (max-width: 768px) {
    padding: 1.2rem 1rem 1.2rem 0.5rem;
    gap: 0.4rem;
    position: relative;
    z-index: 1;
    flex: 1;
    display: flex;
    justify-content: space-between;
  }

  @media (max-width: 480px) {
    padding: 1rem 0.8rem 1rem 0.4rem;
    gap: 0.3rem;
  }
`;

const ListStoreName = styled.h3`
  font-size: 1.2rem;
  font-weight: 700;
  color: #212529;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -0.01em;
    line-height: 1.2;
    margin-bottom: 0.2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.05rem;
    margin-bottom: 0.15rem;
  }
`;

const ListStoreDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  @media (max-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }

  @media (max-width: 480px) {
    gap: 0.4rem;
  }
`;

const ListStoreCategory = styled.span`
  color: #6c757d;
  font-size: 0.9rem;

  @media (max-width: 768px) {
    background: rgba(241, 105, 37, 0.08);
    color: #F16925;
    padding: 0.2rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  @media (max-width: 480px) {
    font-size: 0.65rem;
    padding: 0.15rem 0.5rem;
    border-radius: 6px;
  }
`;

const ListStoreHours = styled.span`
  color: #28a745;
  font-size: 0.9rem;
  font-weight: 600;

  @media (max-width: 768px) {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 0.2rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
  }

  @media (max-width: 480px) {
    font-size: 0.65rem;
    padding: 0.15rem 0.5rem;
    border-radius: 6px;
  }
`;

const ListStoreLocation = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  color: #2c3e50;
  font-size: 0.85rem;
  font-weight: 600;
  background: linear-gradient(135deg, rgba(241, 105, 37, 0.08) 0%, rgba(241, 105, 37, 0.04) 100%);
  border: 1px solid rgba(241, 105, 37, 0.15);
  padding: 0.25rem 0.6rem;
  border-radius: 6px;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
  width: fit-content;
  margin: 0 auto;

  &:hover {
    background: linear-gradient(135deg, rgba(241, 105, 37, 0.12) 0%, rgba(241, 105, 37, 0.06) 100%);
    border-color: rgba(241, 105, 37, 0.25);
  }

  @media (max-width: 768px) {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    gap: 0.25rem;
  }

  @media (max-width: 480px) {
    font-size: 0.65rem;
    padding: 0.15rem 0.45rem;
    border-radius: 4px;
    gap: 0.2rem;
  }
`;

const ListStoreInfoContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;

  @media (max-width: 768px) {
    gap: 0.4rem;
  }

  @media (max-width: 480px) {
    gap: 0.3rem;
  }
`;

const MobileListButton = styled(Link)`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #F16925 0%, #e05a1a 100%);
    color: white;
    padding: 0.6rem 1rem;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.75rem;
    box-shadow: 0 2px 8px rgba(241, 105, 37, 0.25);
    border: none;
    position: relative;
    overflow: hidden;
    min-width: 70px;
    height: fit-content;
    align-self: center;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }
    
    &:active {
      background: linear-gradient(135deg, #d44e14 0%, #c44a14 100%);
      transform: scale(0.95);
      box-shadow: 0 1px 4px rgba(241, 105, 37, 0.4);
    }
  }

  @media (max-width: 480px) {
    padding: 0.5rem 0.8rem;
    font-size: 0.7rem;
    border-radius: 8px;
    min-width: 60px;
  }
`;

// helper to create URL friendly slug
const slugify = (str) =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

// Generate safe slug; if slug empty use fallback id
const safeSlug = (text, fallback) => {
  const s = slugify(text || '');
  return s.length ? s : String(fallback);
};

const StandsVirtuales = () => {
  const [search, setSearch] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('name');
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth > 1024);
  const [imageLoaded, setImageLoaded] = useState({});
  const { stands: rawStands, loading, error, fetchStands } = useStandsStore();
  const { language } = useLanguageStore();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { tenants, fetchTenants } = useTenantListingsStore();

  const content = {
    title: language === 'es' ? 'Directorio de Locales' : 'Store Directory',
    searchPlaceholder: language === 'es' 
      ? 'Buscar locales, productos y marcas...' 
      : 'Search Stores, Product Categories & Brands...',
    sort: language === 'es' ? 'Ordenar' : 'Sort',
    storeOffers: language === 'es' ? 'Ofertas de Locales' : 'Store Offers',
    storeTypes: language === 'es' ? 'Tipos de Locales' : 'Store Types',
    all: language === 'es' ? 'Todos' : 'All',
    retail: language === 'es' ? 'Retail' : 'Retail',
    restaurants: language === 'es' ? 'Restaurantes' : 'Restaurants',
    beauty: language === 'es' ? 'Belleza' : 'Beauty',
    openNow: language === 'es' ? 'Abierto Ahora' : 'Open Now',
    mainLevel: language === 'es' ? 'Nivel Principal' : 'Main Level',
    viewStore: language === 'es' ? 'Ver Local' : 'View Store',
    gridView: language === 'es' ? 'Tarjetas' : 'Cards',
    listView: language === 'es' ? 'Listado' : 'List',
    alphabetical: language === 'es' ? 'Alfabético' : 'Alphabetical',
    category: language === 'es' ? 'Categoría' : 'Category',
    filters: language === 'es' ? 'Filtros' : 'Filters',
    stores: language === 'es' ? 'Locales' : 'Stores',
    unitLabel: language === 'es' ? 'Unidad: ' : 'Unit: '
  };

  const DEFAULT_LOGO = '/logo-2.webp';
  const DEFAULT_BANNER = '/logo-2.webp';

  // Function to get button text based on stand category
  const getButtonText = (standCategory) => {
    if (standCategory === 'Restaurant') {
      return language === 'es' ? 'Ver Stand' : 'View Stand';
    }
    return content.viewStore;
  };

  const handleImageLoad = (standId, imageType) => {
    setImageLoaded(prev => ({
      ...prev,
      [`${standId}_${imageType}`]: true
    }));
  };

  useEffect(() => {
    fetchStands();
  }, [fetchStands]);

  useEffect(() => {
    const category = searchParams.get('category');
    if (category) {
      setActiveFilter(category);
    } else {
      setActiveFilter('all');
    }
  }, [searchParams]);

  useEffect(() => {
    fetchTenants();
  }, [fetchTenants]);

  // Handle responsive sidebar behavior with debounced resize listener
  useEffect(() => {
    // Update state based on current viewport width
    const updateSidebar = () => {
      setSidebarOpen(window.innerWidth > 1024);
    };

    // Debounce wrapper to avoid firing on every single resize event
    let resizeTimeoutId;
    const handleResize = () => {
      clearTimeout(resizeTimeoutId);
      resizeTimeoutId = setTimeout(() => {
        updateSidebar();
      }, 150); // 150 ms debounce – tune as needed
    };

    // Initial check on mount
    updateSidebar();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeoutId);
    };
  }, []);

  const handleFilterClick = (filter) => {
    setActiveFilter(filter);
    const newSearchParams = new URLSearchParams(searchParams);
    if (filter === 'all') {
      newSearchParams.delete('category');
    } else {
      newSearchParams.set('category', filter);
    }
    window.history.pushState({}, '', `${location.pathname}?${newSearchParams.toString()}`);
  };

  // ensure every stand has a slug; if backend slug is null generate from name
  const stands = rawStands.map((s) => ({
    ...s,
    slug: safeSlug(s.slug || s.name, s.id),
  }));

  const tenantMapped = tenants.map((t) => ({
    id: `tenant-${t.id}`,
    name: t.tenant_name,
    banner_url: t.banner_url || DEFAULT_BANNER,
    avatar: t.avatar || DEFAULT_LOGO,
    slug: safeSlug(t.slug || t.tenant_name, t.id),
    category: t.category,
    description: '',
    unit: t.unit,
  }));
  const combined = [...stands, ...tenantMapped];

  const filteredStands = combined.filter((stand) => {
    const searchTerm = search.toLowerCase();
    const matchesSearch = 
      stand.name.toLowerCase().includes(searchTerm) ||
      stand.category?.toLowerCase().includes(searchTerm) ||
      stand.description?.toLowerCase().includes(searchTerm) ||
      stand.description_es?.toLowerCase().includes(searchTerm) ||
      stand.description_en?.toLowerCase().includes(searchTerm);
    
    const matchesFilter = activeFilter === 'all' || stand.category === activeFilter;
    
    return matchesSearch && matchesFilter;
  });

  const sortedStands = [...filteredStands].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'category':
        return (a.category || '').localeCompare(b.category || '');
      default:
        return 0;
    }
  });

  const getLink = (s) => s.id.startsWith('tenant-') ? `/tenant/${s.slug}` : `/stands-virtuales/${s.slug}`;

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <>
        <Navbar />
        <PageWrapper>
          <HeaderSection>
            <HeaderContainer>
              <h1 style={{ color: '#ff4444' }}>{error}</h1>
            </HeaderContainer>
          </HeaderSection>
        </PageWrapper>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <PageWrapper>
        <HeaderSection>
          <HeaderContainer>
            <SearchContainer>
              <SearchBox>
                <Search size={20} color="#6c757d" />
                <SearchInput
                  type="text"
                  placeholder={content.searchPlaceholder}
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </SearchBox>
              <SearchButton>
                <Search size={16} />
              </SearchButton>
            </SearchContainer>
            
            <SortContainer>
              <FilterToggle 
                $active={sidebarOpen}
                onClick={() => {
                  setSidebarOpen(!sidebarOpen);
                }}
              >
                <SlidersHorizontal size={16} />
                {content.filters}
              </FilterToggle>
              
              <SortSelect value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
                <option value="name">{content.alphabetical}</option>
                <option value="category">{content.category}</option>
              </SortSelect>
            </SortContainer>
            
            <ViewToggleContainer>
              <ViewToggle>
                <ViewButton $active={viewMode === 'grid'} onClick={() => setViewMode('grid')}>
                  {content.gridView}
                </ViewButton>
                <ViewButton $active={viewMode === 'list'} onClick={() => setViewMode('list')}>
                  {content.listView}
                </ViewButton>
              </ViewToggle>
            </ViewToggleContainer>
          </HeaderContainer>
        </HeaderSection>

        <SidebarOverlay $isOpen={sidebarOpen} onClick={() => setSidebarOpen(false)} />
        
        <MainContainer $sidebarOpen={sidebarOpen}>
          <Sidebar $isOpen={sidebarOpen}>
            <SidebarCloseButton onClick={() => setSidebarOpen(false)}>
              <X size={20} />
            </SidebarCloseButton>
            
            <SidebarTitle>{content.storeOffers}</SidebarTitle>
            
            <FilterSection>
              <FilterLabel>{content.storeTypes}</FilterLabel>
              <FilterOptions>
                <FilterOption 
                  $active={activeFilter === 'all'} 
                  onClick={() => handleFilterClick('all')}
                >
                  <FaThLarge />
                  {content.all}
                </FilterOption>
                <FilterOption 
                  $active={activeFilter === 'Retail'} 
                  onClick={() => handleFilterClick('Retail')}
                >
                  <FaStore />
                  {content.retail}
                </FilterOption>
                <FilterOption 
                  $active={activeFilter === 'Restaurant'} 
                  onClick={() => handleFilterClick('Restaurant')}
                >
                  <FaUtensils />
                  {content.restaurants}
                </FilterOption>
                <FilterOption 
                  $active={activeFilter === 'Beauty'} 
                  onClick={() => handleFilterClick('Beauty')}
                >
                  <FaSpa />
                  {content.beauty}
                </FilterOption>
              </FilterOptions>
            </FilterSection>
          </Sidebar>

          <ContentArea>
            <ResultsHeader>
              <ResultsCount>
                {sortedStands.length} {content.stores}
              </ResultsCount>
              <DesktopViewToggle>
                <ViewButton $active={viewMode === 'grid'} onClick={() => setViewMode('grid')}>
                  {content.gridView}
                </ViewButton>
                <ViewButton $active={viewMode === 'list'} onClick={() => setViewMode('list')}>
                  {content.listView}
                </ViewButton>
              </DesktopViewToggle>
            </ResultsHeader>

            {viewMode === 'grid' ? (
              <StoresGrid>
                {sortedStands.map((stand, index) => (
                  <StoreCard key={stand.id}>
                    <StoreImageContainer>
                      <StoreBanner>
                        {stand.banner_url && (
                          <StoreBannerImage
                            src={stand.banner_url}
                            alt={`${stand.name} banner`}
                            loading={index < 8 ? 'eager' : 'lazy'}
                            fetchpriority={index < 8 ? 'high' : 'auto'}
                            className={imageLoaded[`${stand.id}_banner`] ? 'loaded' : 'loading'}
                            onLoad={() => handleImageLoad(stand.id, 'banner')}
                          />
                        )}
                      </StoreBanner>
                      <StoreLogoWrapper>
                        <StoreLogo 
                          src={stand.avatar} 
                          alt={stand.name}
                          loading={index < 4 ? 'eager' : 'lazy'}
                          fetchpriority={index < 4 ? 'high' : 'auto'}
                          className={imageLoaded[`${stand.id}_avatar`] ? 'loaded' : 'loading'}
                          onLoad={() => handleImageLoad(stand.id, 'avatar')}
                        />
                      </StoreLogoWrapper>
                    </StoreImageContainer>
                    <StoreContent>
                      <StoreName>{stand.name}</StoreName>
                      {stand.unit && (
                        <StoreUnit>
                          <UnitIcon>
                            <Building2 size={16} />
                          </UnitIcon>
                          <UnitLabel>UNIT:</UnitLabel>
                          <UnitNumbers>
                            {Array.isArray(stand.unit) ? stand.unit.join(', ') : stand.unit}
                          </UnitNumbers>
                        </StoreUnit>
                      )}
                      <StoreCategory>{stand.category}</StoreCategory>
                      <StoreHours>
                        <Clock size={14} />
                        {content.openNow}
                      </StoreHours>
                      <ViewStoreButton to={getLink(stand)}>
                        {getButtonText(stand.category)}
                      </ViewStoreButton>
                    </StoreContent>
                  </StoreCard>
                ))}
              </StoresGrid>
            ) : (
              <StoresList>
                {sortedStands.map((stand, index) => (
                  <StoreListItem key={stand.id}>
                    <ListStoreLogo 
                      src={stand.avatar} 
                      alt={stand.name}
                      loading={index < 4 ? 'eager' : 'lazy'}
                      fetchpriority={index < 4 ? 'high' : 'auto'}
                      className={imageLoaded[`${stand.id}_list_avatar`] ? 'loaded' : 'loading'}
                      onLoad={() => handleImageLoad(stand.id, 'list_avatar')}
                    />
                    <ListStoreInfo>
                      <ListStoreInfoContent>
                        <ListStoreName>{stand.name}</ListStoreName>
                        <ListStoreDetails>
                          <ListStoreCategory>{stand.category}</ListStoreCategory>
                          {stand.units && stand.units.length > 0 && (
                            <ListStoreLocation>
                              <Building2 size={14} />
                              {`${content.unitLabel} ${Array.isArray(stand.units) ? stand.units.join(', ') : stand.units}`}
                            </ListStoreLocation>
                          )}
                          <ListStoreHours>{content.openNow}</ListStoreHours>
                        </ListStoreDetails>
                      </ListStoreInfoContent>
                      <MobileListButton to={getLink(stand)}>
                        {getButtonText(stand.category)}
                      </MobileListButton>
                    </ListStoreInfo>
                    <DesktopListButton to={getLink(stand)}>
                      {getButtonText(stand.category)}
                    </DesktopListButton>
                  </StoreListItem>
                ))}
              </StoresList>
            )}
          </ContentArea>
        </MainContainer>
      </PageWrapper>
      <Footer />
    </>
  );
};

export default StandsVirtuales;