import React from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Play, Info } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PageWrapper = styled.div`
  background-color: #141414;
  min-height: 100vh;
  color: white;
`;

const BannerContainer = styled.div`
  position: relative;
  height: 80vh;
  background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.8)),
              url('/login.webp');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  padding: 0 4rem;
`;

const BannerContent = styled.div`
  max-width: 600px;
  z-index: 1;
`;

const BannerTitle = styled.h1`
  font-size: 3.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
`;

const BannerDescription = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  line-height: 1.5;
`;

const BannerButtons = styled.div`
  display: flex;
  gap: 1rem;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;

  &.play {
    background-color: white;
    color: black;

    &:hover {
      background-color: rgba(255,255,255,0.8);
    }
  }

  &.info {
    background-color: rgba(109, 109, 110, 0.7);
    color: white;

    &:hover {
      background-color: rgba(109, 109, 110, 0.9);
    }
  }
`;

const ContentSection = styled.div`
  padding: 2rem 4rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  position: relative;
`;

const ContentCard = styled.div`
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
    z-index: 2;
  }

  img {
    width: 100%;
    height: 300px;
    object-fit: cover;
  }
`;

const CardInfo = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
`;

const CardTitle = styled.h3`
  font-size: 1rem;
  margin: 0;
`;

const CardDescription = styled.p`
  font-size: 0.8rem;
  margin: 0.5rem 0 0;
  opacity: 0.8;
`;

// Datos mockeados para las cards
const mockContent = [
  {
    id: 1,
    title: "EME Summit 2024",
    description: "Resumen desde el EME Summit 2025",
    image: "https://images.unsplash.com/photo-1560523160-754a9e25c68f?q=80&w=1436&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    year: "2024",
    speaker: {
      name: "Dr. Alejandro Martínez",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      bio: "Experto en desarrollo personal y espiritual con más de 20 años de experiencia"
    },
    fullDescription: "El EME Summit 2024 reúne a los principales expertos en desarrollo personal y espiritual para compartir las últimas tendencias y técnicas en el campo. Una experiencia transformadora que te llevará a un nuevo nivel de consciencia.",
    videoId: "abc123"
  },

];

const RevivePlus = () => {
  const navigate = useNavigate();

  const handleCardClick = (id) => {
    navigate(`/revive-detalle/${id}`);
  };

  return (
    <>
      <Navbar />
      <PageWrapper>
        <BannerContainer>
          <BannerContent>
            <BannerTitle>Revive Plus</BannerTitle>
            <BannerDescription>
              Revive Plus es un contenido exclusivo para los participantes del EME Summit 2025. Revive el evento desde este documental
            </BannerDescription>
            <BannerButtons>
              <Button className="play">
                <Play size={20} />
                Reproducir
              </Button>
              <Button className="info">
                <Info size={20} />
                Más información
              </Button>
            </BannerButtons>
          </BannerContent>
        </BannerContainer>

        <ContentSection>
          <SectionTitle>Contenido Destacado</SectionTitle>
          <ContentGrid>
            {mockContent.map((content) => (
              <ContentCard key={content.id} onClick={() => handleCardClick(content.id)}>
                <img src={content.image} alt={content.title} loading="lazy" />
                <CardInfo>
                  <CardTitle>{content.title}</CardTitle>
                  <CardDescription>{content.description}</CardDescription>
                </CardInfo>
              </ContentCard>
            ))}
          </ContentGrid>
        </ContentSection>
      </PageWrapper>
    </>
  );
};

export default RevivePlus; 